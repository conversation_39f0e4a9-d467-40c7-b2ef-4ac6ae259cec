<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<title>3D Sphere Music Visualizer</title>
<style>
  html, body {
    margin: 0;
    overflow: hidden;
    background: black;
    height: 100%;
    font-family: sans-serif;
  }
  canvas { display: block; }
  #uploadBtn {
    position: absolute;
    top: 20px;
    left: 20px;
    z-index: 10;
    padding: 10px 20px;
    background: rgba(255,255,255,0.1);
    color: white;
    border: none;
    cursor: pointer;
  }
</style>
</head>
<body>
<input type="file" id="uploadBtn" accept="audio/*">
<canvas id="canvas"></canvas>

<script>
const canvas = document.getElementById('canvas');
const ctx = canvas.getContext('2d');
canvas.width = window.innerWidth;
canvas.height = window.innerHeight;

let audioCtx, analyser, source, dataArray;
let particles = [];
const particleCount = 800;
const sphereRadius = 200;

function randomSpherePoint(radius) {
  const u = Math.random();
  const v = Math.random();
  const theta = 2 * Math.PI * u;
  const phi = Math.acos(2 * v - 1);
  const x = radius * Math.sin(phi) * Math.cos(theta);
  const y = radius * Math.sin(phi) * Math.sin(theta);
  const z = radius * Math.cos(phi);
  return {x, y, z, original: {x, y, z}, scale: 1};
}

// Initialize particles
for(let i=0; i<particleCount; i++){
  particles.push(randomSpherePoint(sphereRadius));
}

// Projection
function project(p) {
  const fov = 500;
  const scale = fov / (fov + p.z);
  return {
    x: canvas.width/2 + p.x * scale,
    y: canvas.height/2 + p.y * scale,
    scale
  };
}

// Rotate sphere
function rotateY(p, angle){
  const cos = Math.cos(angle);
  const sin = Math.sin(angle);
  const x = p.x * cos - p.z * sin;
  const z = p.x * sin + p.z * cos;
  return {...p, x, z};
}

// Audio setup
function setupAudio(file) {
  if(audioCtx) audioCtx.close();
  audioCtx = new (window.AudioContext || window.webkitAudioContext)();
  analyser = audioCtx.createAnalyser();
  analyser.fftSize = 1024;
  const reader = new FileReader();
  reader.onload = (e) => {
    audioCtx.decodeAudioData(e.target.result, buffer => {
      const audioSource = audioCtx.createBufferSource();
      audioSource.buffer = buffer;
      audioSource.connect(analyser);
      analyser.connect(audioCtx.destination);
      audioSource.start(0);
      source = audioSource;
      dataArray = new Uint8Array(analyser.frequencyBinCount);
      animate();
    });
  };
  reader.readAsArrayBuffer(file);
}

// Animation loop
let angle = 0;
function animate(){
  ctx.clearRect(0,0,canvas.width,canvas.height);
  if(analyser) analyser.getByteTimeDomainData(dataArray);
  
  angle += 0.002;
  for(let i=0; i<particles.length; i++){
    let p = rotateY(particles[i], angle);
    
    // Use waveform to scale particle distance
    if(dataArray){
      const audioValue = dataArray[i % dataArray.length] / 128 - 1; // -1 to 1
      const scale = 1 + audioValue * 0.5; // Adjust pulsation
      p.x = p.original.x * scale;
      p.y = p.original.y * scale;
      p.z = p.original.z * scale;
    }
    
    const proj = project(p);
    ctx.beginPath();
    ctx.arc(proj.x, proj.y, 2 * proj.scale, 0, 2*Math.PI);
    ctx.fillStyle = `rgba(0, 200, 255, ${0.6 * proj.scale})`;
    ctx.fill();
  }
  requestAnimationFrame(animate);
}

document.getElementById('uploadBtn').addEventListener('change', (e)=>{
  const file = e.target.files[0];
  if(file) setupAudio(file);
});

window.addEventListener('resize', () => {
  canvas.width = window.innerWidth;
  canvas.height = window.innerHeight;
});
</script>
</body>
</html>
