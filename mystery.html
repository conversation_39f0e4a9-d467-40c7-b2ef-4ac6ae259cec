<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Transformer Cube</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #0a0a0a, #1a1a2e, #16213e);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            font-family: 'Arial', sans-serif;
            overflow: hidden;
        }

        .container {
            perspective: 1200px;
            position: relative;
        }

        .transformer-cube {
            width: 200px;
            height: 200px;
            position: relative;
            transform-style: preserve-3d;
            transition: transform 3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            cursor: pointer;
        }

        .face {
            position: absolute;
            width: 200px;
            height: 200px;
            background: linear-gradient(45deg, #2c3e50, #34495e);
            border: 3px solid #e74c3c;
            box-shadow: 
                inset 0 0 20px rgba(231, 76, 60, 0.3),
                0 0 30px rgba(231, 76, 60, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            color: #ecf0f1;
            text-shadow: 0 0 10px rgba(231, 76, 60, 0.8);
            transition: all 2s ease-in-out;
        }

        .face::before {
            content: '';
            position: absolute;
            top: 10px;
            left: 10px;
            right: 10px;
            bottom: 10px;
            border: 1px solid rgba(231, 76, 60, 0.5);
            background: repeating-linear-gradient(
                45deg,
                transparent,
                transparent 10px,
                rgba(231, 76, 60, 0.1) 10px,
                rgba(231, 76, 60, 0.1) 20px
            );
        }

        .front { transform: rotateY(0deg) translateZ(100px); }
        .back { transform: rotateY(180deg) translateZ(100px); }
        .right { transform: rotateY(90deg) translateZ(100px); }
        .left { transform: rotateY(-90deg) translateZ(100px); }
        .top { transform: rotateX(90deg) translateZ(100px); }
        .bottom { transform: rotateX(-90deg) translateZ(100px); }

        /* Transformation states */
        .transformer-cube.transforming {
            animation: complexTransform 4s ease-in-out infinite;
        }

        .transformer-cube.transforming .front {
            transform: rotateY(0deg) translateZ(100px) rotateX(180deg) translateY(-50px);
            animation: frontTransform 4s ease-in-out infinite;
        }

        .transformer-cube.transforming .back {
            transform: rotateY(180deg) translateZ(100px) rotateX(-180deg) translateY(50px);
            animation: backTransform 4s ease-in-out infinite;
        }

        .transformer-cube.transforming .right {
            transform: rotateY(90deg) translateZ(100px) rotateZ(90deg) translateX(100px);
            animation: rightTransform 4s ease-in-out infinite;
        }

        .transformer-cube.transforming .left {
            transform: rotateY(-90deg) translateZ(100px) rotateZ(-90deg) translateX(-100px);
            animation: leftTransform 4s ease-in-out infinite;
        }

        .transformer-cube.transforming .top {
            transform: rotateX(90deg) translateZ(100px) rotateY(180deg) translateZ(150px);
            animation: topTransform 4s ease-in-out infinite;
        }

        .transformer-cube.transforming .bottom {
            transform: rotateX(-90deg) translateZ(100px) rotateY(180deg) translateZ(-150px);
            animation: bottomTransform 4s ease-in-out infinite;
        }

        @keyframes complexTransform {
            0% { transform: rotateX(0deg) rotateY(0deg) rotateZ(0deg); }
            25% { transform: rotateX(90deg) rotateY(180deg) rotateZ(45deg) scale(1.2); }
            50% { transform: rotateX(180deg) rotateY(360deg) rotateZ(90deg) scale(0.8); }
            75% { transform: rotateX(270deg) rotateY(540deg) rotateZ(135deg) scale(1.1); }
            100% { transform: rotateX(360deg) rotateY(720deg) rotateZ(180deg); }
        }

        @keyframes frontTransform {
            0%, 100% { transform: rotateY(0deg) translateZ(100px); }
            25% { transform: rotateY(0deg) translateZ(100px) rotateX(90deg) translateY(-100px); }
            50% { transform: rotateY(0deg) translateZ(200px) rotateX(180deg) translateY(-50px); }
            75% { transform: rotateY(0deg) translateZ(150px) rotateX(270deg) translateY(25px); }
        }

        @keyframes backTransform {
            0%, 100% { transform: rotateY(180deg) translateZ(100px); }
            25% { transform: rotateY(180deg) translateZ(100px) rotateX(-90deg) translateY(100px); }
            50% { transform: rotateY(180deg) translateZ(200px) rotateX(-180deg) translateY(50px); }
            75% { transform: rotateY(180deg) translateZ(150px) rotateX(-270deg) translateY(-25px); }
        }

        @keyframes rightTransform {
            0%, 100% { transform: rotateY(90deg) translateZ(100px); }
            25% { transform: rotateY(90deg) translateZ(100px) rotateZ(45deg) translateX(50px); }
            50% { transform: rotateY(90deg) translateZ(200px) rotateZ(90deg) translateX(100px); }
            75% { transform: rotateY(90deg) translateZ(150px) rotateZ(135deg) translateX(75px); }
        }

        @keyframes leftTransform {
            0%, 100% { transform: rotateY(-90deg) translateZ(100px); }
            25% { transform: rotateY(-90deg) translateZ(100px) rotateZ(-45deg) translateX(-50px); }
            50% { transform: rotateY(-90deg) translateZ(200px) rotateZ(-90deg) translateX(-100px); }
            75% { transform: rotateY(-90deg) translateZ(150px) rotateZ(-135deg) translateX(-75px); }
        }

        @keyframes topTransform {
            0%, 100% { transform: rotateX(90deg) translateZ(100px); }
            25% { transform: rotateX(90deg) translateZ(100px) rotateY(90deg) translateZ(50px); }
            50% { transform: rotateX(90deg) translateZ(200px) rotateY(180deg) translateZ(150px); }
            75% { transform: rotateX(90deg) translateZ(150px) rotateY(270deg) translateZ(100px); }
        }

        @keyframes bottomTransform {
            0%, 100% { transform: rotateX(-90deg) translateZ(100px); }
            25% { transform: rotateX(-90deg) translateZ(100px) rotateY(-90deg) translateZ(-50px); }
            50% { transform: rotateX(-90deg) translateZ(200px) rotateY(-180deg) translateZ(-150px); }
            75% { transform: rotateX(-90deg) translateZ(150px) rotateY(-270deg) translateZ(-100px); }
        }

        .controls {
            position: absolute;
            bottom: 50px;
            left: 50%;
            transform: translateX(-50%);
            text-align: center;
        }

        .btn {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
            color: white;
            border: none;
            padding: 15px 30px;
            margin: 0 10px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            text-transform: uppercase;
            box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
            transition: all 0.3s ease;
        }

        .btn:hover {
            background: linear-gradient(45deg, #c0392b, #a93226);
            box-shadow: 0 6px 20px rgba(231, 76, 60, 0.5);
            transform: translateY(-2px);
        }

        .title {
            position: absolute;
            top: 50px;
            left: 50%;
            transform: translateX(-50%);
            color: #ecf0f1;
            font-size: 32px;
            font-weight: bold;
            text-shadow: 0 0 20px rgba(231, 76, 60, 0.8);
            text-align: center;
        }

        /* Particle effects */
        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: #e74c3c;
            border-radius: 50%;
            pointer-events: none;
            animation: particle 2s linear infinite;
        }

        @keyframes particle {
            0% {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
            100% {
                opacity: 0;
                transform: translateY(-100px) scale(0);
            }
        }
    </style>
</head>
<body>
    <div class="title">TRANSFORMER CUBE</div>
    
    <div class="container">
        <div class="transformer-cube" id="cube">
            <div class="face front">AUTOBOT</div>
            <div class="face back">PRIME</div>
            <div class="face right">TRANS</div>
            <div class="face left">FORM</div>
            <div class="face top">ROLL</div>
            <div class="face bottom">OUT</div>
        </div>
    </div>

    <div class="controls">
        <button class="btn" onclick="transform()">TRANSFORM</button>
        <button class="btn" onclick="reset()">RESET</button>
    </div>

    <script>
        const cube = document.getElementById('cube');
        let isTransforming = false;

        function transform() {
            if (!isTransforming) {
                cube.classList.add('transforming');
                isTransforming = true;
                createParticles();
            }
        }

        function reset() {
            cube.classList.remove('transforming');
            isTransforming = false;
        }

        function createParticles() {
            for (let i = 0; i < 20; i++) {
                setTimeout(() => {
                    const particle = document.createElement('div');
                    particle.className = 'particle';
                    particle.style.left = Math.random() * window.innerWidth + 'px';
                    particle.style.top = Math.random() * window.innerHeight + 'px';
                    document.body.appendChild(particle);
                    
                    setTimeout(() => {
                        particle.remove();
                    }, 2000);
                }, i * 100);
            }
        }

        // Auto-rotate on hover
        cube.addEventListener('mouseenter', () => {
            if (!isTransforming) {
                cube.style.transform = 'rotateX(15deg) rotateY(15deg)';
            }
        });

        cube.addEventListener('mouseleave', () => {
            if (!isTransforming) {
                cube.style.transform = 'rotateX(0deg) rotateY(0deg)';
            }
        });

        // Click to transform
        cube.addEventListener('click', transform);
    </script>
</body>
</html>