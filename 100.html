<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8" />
<meta name="viewport" content="width=device-width,initial-scale=1" />
<title>Spacetime Field — 2D + 3D Music Visualizer (Self-contained)</title>
<style>
  :root {
    --bg1: #03061a;
    --bg2: #07132a;
    --glass: rgba(255,255,255,0.04);
    --accent: #66ffe0;
    --muted: rgba(255,255,255,0.7);
  }
  html,body{height:100%;margin:0;background:linear-gradient(180deg,var(--bg1),var(--bg2));font-family:Inter,system-ui,-apple-system,Segoe UI,Roboto,Arial;}
  #app{position:fixed;inset:0;display:flex;align-items:stretch;justify-content:center;overflow:hidden;}
  canvas{display:block;width:100vw;height:100vh;}
  .panel{
    position: absolute; left:16px; top:16px; width:320px; padding:12px; border-radius:12px;
    background: linear-gradient(180deg, rgba(255,255,255,0.02), rgba(255,255,255,0.01));
    backdrop-filter: blur(6px); color:var(--muted); box-shadow: 0 8px 30px rgba(0,0,0,0.6); font-size:13px;
  }
  .panel h2{margin:0 0 8px 0;color:var(--accent);font-size:15px}
  .row{display:flex;gap:8px;align-items:center;margin:8px 0;}
  label{flex:1;font-size:12px}
  input[type="range"]{flex:1}
  .controls{display:flex;gap:8px;flex-wrap:wrap}
  button{background:transparent;border:1px solid rgba(255,255,255,0.06);padding:8px 10px;border-radius:8px;color:var(--muted);cursor:pointer}
  .bottom{position:absolute;left:16px;bottom:16px;display:flex;gap:12px;align-items:center;color:var(--muted);font-size:13px}
  .small{font-size:12px;color:rgba(255,255,255,0.6)}
  @media (max-width:640px){ .panel{left:10px;right:10px;width:auto;} .bottom{flex-direction:column;align-items:flex-start} }
</style>
</head>
<body>
<div id="app">
  <canvas id="c"></canvas>

  <div class="panel" id="ui">
    <h2>Spacetime Field • Visualizer</h2>

    <div class="row">
      <label class="small">Audio</label>
    </div>
    <div class="row controls">
      <input id="file" type="file" accept="audio/*" />
      <button id="micBtn">Mic</button>
      <button id="playBtn">Play</button>
    </div>

    <div class="row"><label>Rings</label><input id="rings" type="range" min="4" max="48" value="18"></div>
    <div class="row"><label>Segments</label><input id="segments" type="range" min="8" max="256" value="120"></div>
    <div class="row"><label>Warp Strength</label><input id="warp" type="range" min="0" max="3" step="0.01" value="1.25"></div>
    <div class="row"><label>Audio Sensitivity</label><input id="sensitivity" type="range" min="0.2" max="6" step="0.01" value="1.6"></div>
    <div class="row"><label>Rotation</label><input id="spin" type="range" min="0" max="2" step="0.01" value="0.5"></div>

    <div style="margin-top:8px" class="small">Polar mesh (rings + spokes) creates a tunnel/field. Increase segments for smoother curves. Use "Mic" or upload an audio file. Performance mode toggles drawing detail.</div>
    <div style="margin-top:8px;display:flex;gap:8px">
      <button id="perfToggle">Toggle Perf Mode</button>
      <button id="resetBtn">Reset</button>
    </div>
  </div>

  <div class="bottom" id="status">
    <div id="track" class="small">No audio loaded</div>
    <div class="small">Reactive • concentric spacetime field</div>
  </div>
</div>

<!-- hidden audio element used if user loads an <audio> -->
<audio id="audioEl" crossorigin="anonymous"></audio>

<script>
/* Spacetime Field + 2D Visualizer
   - Polar mesh (rings & spokes) projected to perspective to create tunnel/field
   - Points displaced by sin waves + audio FFT
   - Mesh drawn with lines and soft glow using shadowBlur + additive composite
   - Controls for rings, segments, warp strength, sensitivity, rotation
   - File upload or mic input
*/

const canvas = document.getElementById('c');
const ctx = canvas.getContext('2d', { alpha: true });
let W = canvas.width = innerWidth;
let H = canvas.height = innerHeight;

const fileInput = document.getElementById('file');
const micBtn = document.getElementById('micBtn');
const playBtn = document.getElementById('playBtn');
const perfToggle = document.getElementById('perfToggle');
const resetBtn = document.getElementById('resetBtn');

const ringsSlider = document.getElementById('rings');
const segmentsSlider = document.getElementById('segments');
const warpSlider = document.getElementById('warp');
const sensSlider = document.getElementById('sensitivity');
const spinSlider = document.getElementById('spin');

const trackInfo = document.getElementById('track');

let audioCtx = null;
let analyser = null;
let dataArray = null;
let timeArray = null;
let sourceNode = null;
let isPlaying = false;
let usingMic = false;

let rings = parseInt(ringsSlider.value);       // concentric rings count
let segments = parseInt(segmentsSlider.value); // segments per ring
let warp = parseFloat(warpSlider.value);
let sensitivity = parseFloat(sensSlider.value);
let spin = parseFloat(spinSlider.value);

let perfMode = false;

// geometry: polar grid: rings x segments
let mesh = []; // mesh[r][s] -> {x,y,z}
function buildMesh() {
  mesh = [];
  // rings: 0 = center, rings-1 = outermost
  for (let r = 0; r < rings; r++) {
    const ring = [];
    const radius = (r / (rings - 1)) * Math.min(W,H) * 0.42 + 6;
    for (let s = 0; s < segments; s++) {
      const theta = (s / segments) * Math.PI * 2;
      const x = Math.cos(theta) * radius;
      const y = Math.sin(theta) * radius;
      ring.push({ x, y, z: 0, r, s, theta, radius });
    }
    mesh.push(ring);
  }
}

// simple perspective projection (camera at z = camZ looking at origin)
function project3D(x,y,z, camZ=800, fov=700) {
  // rotate around Y and X via global angles set in draw loop
  // For small cost, rotation applied on already displaced coords if needed.
  const scale = fov / (fov + z + camZ);
  const sx = x * scale + W/2;
  const sy = y * scale + H/2;
  return { sx, sy, scale, z };
}

// audio helpers
function ensureAudio() {
  if (!audioCtx) {
    audioCtx = new (window.AudioContext || window.webkitAudioContext)();
    analyser = audioCtx.createAnalyser();
    analyser.fftSize = 2048;
    const bufferLen = analyser.frequencyBinCount;
    dataArray = new Uint8Array(bufferLen);
    timeArray = new Uint8Array(bufferLen);
  }
}

fileInput.addEventListener('change', async (e) => {
  const f = e.target.files && e.target.files[0];
  if (!f) return;
  ensureAudio();
  stopAudio();
  const arrayBuffer = await f.arrayBuffer();
  const decoded = await audioCtx.decodeAudioData(arrayBuffer);
  // use buffer source (looped so play/pause feels instant)
  sourceNode = audioCtx.createBufferSource();
  sourceNode.buffer = decoded;
  sourceNode.loop = true;
  sourceNode.connect(analyser);
  analyser.connect(audioCtx.destination);
  sourceNode.start(0);
  isPlaying = true;
  usingMic = false;
  playBtn.textContent = 'Pause';
  trackInfo.textContent = f.name;
});

micBtn.addEventListener('click', async () => {
  ensureAudio();
  stopAudio();
  try {
    const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
    sourceNode = audioCtx.createMediaStreamSource(stream);
    sourceNode.connect(analyser);
    // don't connect analyser to destination to avoid echo
    isPlaying = true;
    usingMic = true;
    playBtn.textContent = 'Pause';
    trackInfo.textContent = 'Microphone';
  } catch (err) {
    alert('Mic access denied or unavailable');
  }
});

playBtn.addEventListener('click', async () => {
  ensureAudio();
  if (!isPlaying) {
    if (audioCtx.state === 'suspended') await audioCtx.resume();
    // If user loaded an <audio> element via file input manually (not used here), could support later
    if (!sourceNode) {
      // fallback to mic if nothing loaded
      try { await micBtn.click(); } catch(e){}
      return;
    }
    isPlaying = true;
    playBtn.textContent = 'Pause';
  } else {
    stopAudio();
  }
});

function stopAudio() {
  if (!audioCtx) return;
  // stop buffer sources
  try {
    if (usingMic && sourceNode && sourceNode.mediaStream) {
      const tracks = sourceNode.mediaStream.getTracks ? sourceNode.mediaStream.getTracks() : [];
      tracks.forEach(t => t.stop && t.stop());
    }
  } catch(err){}
  try { if (sourceNode && sourceNode.stop && !usingMic) sourceNode.stop(); } catch(e){}
  try { if (sourceNode) sourceNode.disconnect(); } catch(e){}
  sourceNode = null;
  isPlaying = false;
  usingMic = false;
  playBtn.textContent = 'Play';
  trackInfo.textContent = 'No audio loaded';
}

// UI sliders
ringsSlider.addEventListener('input', () => { rings = parseInt(ringsSlider.value); buildMesh(); });
segmentsSlider.addEventListener('input', () => { segments = parseInt(segmentsSlider.value); buildMesh(); });
warpSlider.addEventListener('input', () => { warp = parseFloat(warpSlider.value); });
sensSlider.addEventListener('input', () => { sensitivity = parseFloat(sensSlider.value); });
spinSlider.addEventListener('input', () => { spin = parseFloat(spinSlider.value); });

perfToggle.addEventListener('click', () => {
  perfMode = !perfMode;
  perfToggle.textContent = perfMode ? 'Perf: ON' : 'Perf: OFF';
  // when perf on, reduce segments if very high
  if (perfMode && segments > 180) { segments = 180; segmentsSlider.value = 180; buildMesh(); }
});
resetBtn.addEventListener('click', () => {
  ringsSlider.value = 18; segmentsSlider.value = 120; warpSlider.value = 1.25; sensSlider.value = 1.6; spinSlider.value = 0.5;
  rings = 18; segments = 120; warp = 1.25; sensitivity = 1.6; spin = 0.5;
  buildMesh();
});

addEventListener('resize', () => { W = canvas.width = innerWidth; H = canvas.height = innerHeight; buildMesh(); });

// build initial mesh
buildMesh();

// beat/energy helper (simple energy)
function computeEnergy(freq) {
  if (!analyser) return 0;
  analyser.getByteFrequencyData(dataArray);
  // compute RMS-like energy
  let sum = 0;
  for (let i = 0; i < dataArray.length; i++) {
    const v = dataArray[i] / 255;
    sum += v * v;
  }
  return Math.sqrt(sum / dataArray.length);
}

// draw loop
let lastT = performance.now();
let spinAngle = 0;
function draw() {
  const now = performance.now();
  const dt = (now - lastT) / 1000;
  lastT = now;

  // fetch audio data
  if (analyser) {
    analyser.getByteFrequencyData(dataArray);
    analyser.getByteTimeDomainData(timeArray);
  }

  // clear - subtle fade effect for trails
  ctx.clearRect(0,0,W,H);
  // background gradient
  const bg = ctx.createLinearGradient(0,0,0,H);
  bg.addColorStop(0, 'rgba(2,6,14,0.0)');
  bg.addColorStop(1, 'rgba(2,8,20,0.35)');
  ctx.fillStyle = bg;
  ctx.fillRect(0,0,W,H);

  // compute audio metrics
  const energy = analyser ? computeEnergy(dataArray) * sensitivity : 0;
  const bass = analyser ? getBandEnergy(20,250) * sensitivity : 0;
  const mid = analyser ? getBandEnergy(250,2000) * sensitivity : 0;
  const high = analyser ? getBandEnergy(2000,10000) * sensitivity : 0;

  // update spin for camera rotation
  spinAngle += (0.2 * spin + mid * 0.003) * dt * 60;

  // draw multiple layers to create depth tunnel
  // we'll render from outermost ring inward for painter's algo
  ctx.save();
  ctx.globalCompositeOperation = 'lighter';
  // subtle glow: shadowBlur on lines
  ctx.lineWidth = perfMode ? 0.9 : 1.6;
  ctx.shadowColor = 'rgba(90,230,200,0.9)';
  ctx.shadowBlur = perfMode ? 6 : 26;

  // iterate rings -> segments to compute displaced points
  // We'll draw rings as line-strips and spokes as connecting radial lines for mesh look
  const t = now * 0.001;
  // audio-based pulsation
  const pulse = 1 + Math.sin(t * 2.2) * 0.06 + energy * 0.5;

  // Precompute displaced projected points for speed
  const projected = [];
  for (let r = 0; r < mesh.length; r++) {
    projected[r] = [];
    for (let s = 0; s < mesh[r].length; s++) {
      const p = mesh[r][s];
      // base polar coords
      const baseRadius = p.radius;
      // distance-from-center factor (0..1)
      const rr = r / (rings - 1);
      // audio index mapping: sample frequency bin influenced by ring
      let audioIdx = 0;
      if (analyser && dataArray) {
        // bias sample to lower bins for inner rings and higher bins for outer rings
        const idx = Math.floor((s / segments) * dataArray.length * 0.6 + rr * dataArray.length * 0.4);
        audioIdx = dataArray[Math.max(0, Math.min(dataArray.length-1, idx))] / 255;
      }
      // dynamic z displacement = combination of radial wave + rotation wave + audio influence
      const wave1 = Math.sin(t * 1.6 + rr * 5.0 + s * 0.02 + spinAngle * 0.03) * 20 * (0.8 + rr * 2.4);
      const wave2 = Math.sin((s / segments) * Math.PI * 2 * 3 + t * 2.4) * 12 * (1 - rr);
      const audioInfluence = audioIdx * (10 + rr * 80) * (1 + energy * 2.4);
      // spacetime warp (radial contraction/expansion)
      const radialWarp = Math.sin(t * 2.2 + rr * 6 + s * 0.08) * (4 + rr * 18) * (warp * 0.35);
      // final z used for perspective and color
      const z = (wave1 + wave2 + audioInfluence + radialWarp) * (0.8 + energy * 0.8);
      // rotate the 3D point around z-axis by spinAngle to create orbit
      const cosA = Math.cos(spinAngle * 0.01 + rr * 0.06);
      const sinA = Math.sin(spinAngle * 0.01 + rr * 0.06);
      const rx = p.x * cosA - p.y * sinA;
      const ry = p.x * sinA + p.y * cosA;
      // perspective projection
      const proj = project3D(rx, ry, z, 900, 700);
      // color factor by depth + audio
      const depthFactor = Math.max(0.08, Math.min(1, 1 - (z / 1200))); // closer => larger
      projected[r][s] = { x: proj.sx, y: proj.sy, z, depthFactor, audioIdx, rr, theta: p.theta };
    }
  }

  // draw spokes (radial lines) first (outer->inner)
  for (let s = 0; s < segments; s += (perfMode ? 2 : 1)) {
    ctx.beginPath();
    for (let r = mesh.length - 1; r >= 0; r--) {
      const p = projected[r][s];
      if (!p) continue;
      if (r === mesh.length - 1) ctx.moveTo(p.x, p.y);
      else ctx.lineTo(p.x, p.y);
    }
    // color gradient based on average audio on that spoke
    const avgAudio = (() => {
      let sum=0,c=0;
      for (let rr=0; rr<projected.length; rr++){ sum += projected[rr][s].audioIdx; c++; }
      return c ? sum/c : 0;
    })();
    const alpha = 0.06 + Math.min(0.9, avgAudio * 0.9 + 0.06);
    ctx.strokeStyle = `rgba(${Math.floor(40+avgAudio*200)},${Math.floor(220-avgAudio*40)},${Math.floor(200+avgAudio*55)},${alpha})`;
    ctx.stroke();
  }

  // draw rings (concentric) as line strips with color by depth
  for (let r = mesh.length - 1; r >= 0; r--) {
    ctx.beginPath();
    for (let s = 0; s < projected[r].length; s += (perfMode ? 2 : 1)) {
      const p = projected[r][s];
      if (!p) continue;
      if (s === 0) ctx.moveTo(p.x, p.y);
      else ctx.lineTo(p.x, p.y);
    }
    // close ring
    if (projected[r].length) {
      const p0 = projected[r][0];
      ctx.lineTo(p0.x, p0.y);
    }
    // color: inner rings teal-blue, outer rings more purple - brightness by depth
    const df = projected[r][0] ? projected[r][0].depthFactor : 0.5;
    const hue = 185 + (r / rings) * 60; // from teal to purple
    const alpha = 0.04 + df * 0.5 + Math.min(0.35, energy * 0.5);
    ctx.strokeStyle = `hsla(${hue},80%,65%,${alpha})`;
    ctx.stroke();
  }

  // draw nodes as soft dots at some sampled intervals
  ctx.shadowBlur = perfMode ? 8 : 22;
  ctx.lineWidth = 0;
  for (let r = 0; r < projected.length; r+= Math.max(1, Math.floor(rings / (perfMode ? 6 : 12)))) {
    for (let s = 0; s < projected[r].length; s += Math.max(1, Math.floor(segments / (perfMode ? 10 : 40)))) {
      const p = projected[r][s];
      // node size based on depth and high energy
      const size = Math.max(0.8, (1.6 + p.depthFactor * 3.6) * (1 + high * 1.8) );
      ctx.beginPath();
      ctx.fillStyle = `rgba(200,255,235,${0.12 + p.depthFactor * 0.5 + p.audioIdx*0.25})`;
      ctx.arc(p.x, p.y, size, 0, Math.PI*2);
      ctx.fill();
    }
  }

  ctx.restore();

  // 2D Spectrum + waveform overlay bottom
  drawSpectrumAndWaveform(dataArray, timeArray, now, energy);

  requestAnimationFrame(draw);
}

// helper: get energy in frequency band (f1..f2)
function getBandEnergy(f1,f2) {
  if (!analyser || !dataArray) return 0;
  const sampleRate = audioCtx ? audioCtx.sampleRate : 44100;
  const binFreq = sampleRate / analyser.fftSize;
  const start = Math.max(0, Math.floor(f1 / binFreq));
  const end = Math.min(dataArray.length-1, Math.floor(f2 / binFreq));
  let sum = 0;
  let count = 0;
  for (let i = start; i <= end; i++) {
    sum += dataArray[i] / 255;
    count++;
  }
  return count ? sum / count : 0;
}

function drawSpectrumAndWaveform(freqs, times, now, energy) {
  const specH = Math.min(140, H * 0.16);
  const specW = Math.min(W * 0.9, 1400);
  const specX = (W - specW) / 2;
  const specY = H - specH - 28;

  // panel
  ctx.save();
  ctx.fillStyle = 'rgba(6,12,18,0.56)';
  roundedRect(ctx, specX - 8, specY - 10, specW + 16, specH + 20, 12);
  ctx.fill();

  // draw bars
  const barCount = perfMode ? 48 : 96;
  const barW = specW / barCount;
  if (freqs) {
    for (let i = 0; i < barCount; i++) {
      const idx = Math.floor(i * (freqs.length / barCount));
      const v = freqs[idx] / 255;
      const h = Math.pow(v, 1.6) * specH;
      const x = specX + i * barW;
      const y = specY + specH - h;
      // gradient fill effect by height
      ctx.fillStyle = `rgba(${40 + v*200}, ${180 + v*50}, ${200 + v*40}, ${0.12 + v*0.3})`;
      ctx.fillRect(x + 1, y, barW - 2, h);
    }
  }

  // waveform line
  if (times) {
    ctx.beginPath();
    const step = Math.max(1, Math.floor(times.length / specW));
    for (let i = 0; i < specW; i++) {
      const idx = Math.floor((i / specW) * times.length);
      const val = (times[idx] - 128) / 128; // -1..1
      const y = specY + specH * 0.5 + val * (specH * 0.28) * (1 + energy * 1.8);
      if (i === 0) ctx.moveTo(specX + i, y);
      else ctx.lineTo(specX + i, y);
    }
    ctx.strokeStyle = 'rgba(200,255,235,0.9)';
    ctx.lineWidth = 1.6;
    ctx.stroke();
  }

  // center pulse
  ctx.beginPath();
  ctx.fillStyle = `rgba(100,235,195,${0.06 + Math.min(0.8, energy * 0.8)})`;
  ctx.arc(W/2, specY + specH/2, 12 + energy * 30, 0, Math.PI*2);
  ctx.fill();

  // text
  ctx.fillStyle = 'rgba(200,230,220,0.7)';
  ctx.font = '12px system-ui, Arial';
  ctx.fillText('Spacetime Field • concentric rings + spokes • upload audio or use mic', 18, H - 10);
  ctx.restore();
}

function roundedRect(ctx, x, y, w, h, r) {
  ctx.beginPath();
  ctx.moveTo(x+r, y);
  ctx.arcTo(x+w, y, x+w, y+h, r);
  ctx.arcTo(x+w, y+h, x, y+h, r);
  ctx.arcTo(x, y+h, x, y, r);
  ctx.arcTo(x, y, x+w, y, r);
  ctx.closePath();
}

// start loop
requestAnimationFrame(draw);

// helpers: resume audio context on gesture (autoplay policy)
['click','touchstart','keydown'].forEach(evt => {
  window.addEventListener(evt, async function resumeOnce() {
    if (audioCtx && audioCtx.state === 'suspended') {
      try { await audioCtx.resume(); } catch(e){}
    }
    window.removeEventListener(evt, resumeOnce);
  }, { once:true });
});

// prevent right-click menu on canvas
canvas.addEventListener('contextmenu', e => e.preventDefault());

</script>
</body>
</html>
