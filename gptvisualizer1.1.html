<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width,initial-scale=1">
<title>Visualizer Wall — Multi Instance</title>
<style>
  :root{--bg:#05060b;--panel:rgba(10,12,18,0.6);--text:#dfefff}
  html,body{margin:0;height:100%;background:var(--bg);font-family:Inter,system-ui,Arial;color:var(--text);overflow:hidden}
  #container{display:grid;width:100%;height:100%;grid-gap:4px;background:#000}
  .viz{width:100%;height:100%;display:block}
  .panel-toggle{position:absolute;top:10px;left:10px;z-index:30;background:rgba(0,0,0,0.6);border:none;color:#fff;padding:4px 8px;border-radius:6px;cursor:pointer}
  .ui{position:absolute;top:0;left:0;z-index:20;background:var(--panel);padding:10px;border-radius:0 0 10px 0;backdrop-filter:blur(6px);width:360px;transition:transform 0.3s ease}
  .ui.collapsed{transform:translateX(-95%)}
  .row{display:flex;gap:8px;align-items:center;margin:6px 0}
  label{font-size:12px;width:80px}
  input[type=range]{flex:1}
  button,input[type=file],select{background:transparent;border:1px solid rgba(255,255,255,0.2);color:var(--text);padding:4px 6px;border-radius:6px;cursor:pointer}
</style>
</head>
<body>
<button class="panel-toggle" id="toggleBtn">☰</button>
<div class="ui" id="panel">
  <h3>Visualizer Wall</h3>
  <div class="row"><label>Audio</label><input id="file" type="file" accept="audio/*"><button id="play">Play</button><button id="mic">Mic</button></div>
  <div class="row"><label>Visualizers</label><input id="count" type="range" min="1" max="9" value="4"><span id="countVal">4</span></div>
  <div class="row"><label>Bars</label><input id="bars" type="range" min="16" max="256" value="128"><span id="barsVal">128</span></div>
  <div class="row"><label>Sens</label><input id="sensitivity" type="range" min="0.3" max="6" step="0.05" value="1.6"><span id="sensVal">1.60</span></div>
  <div class="row"><label>Rotate</label><input id="rotSpeed" type="range" min="-1.5" max="1.5" step="0.01" value="0.15"><span id="rotVal">0.15</span></div>
  <div class="row"><label>Palette</label><select id="palette"><option value="neon">Neon</option><option value="fire">Fire</option><option value="green">Green</option></select></div>
</div>
<div id="container"></div>

<script>
(()=> {
  const container=document.getElementById('container');
  const panel=document.getElementById('panel');
  const toggleBtn=document.getElementById('toggleBtn');
  toggleBtn.addEventListener('click',()=>panel.classList.toggle('collapsed'));

  const countSlider=document.getElementById('count'),countVal=document.getElementById('countVal');
  const barsSlider=document.getElementById('bars'),barsVal=document.getElementById('barsVal');
  const sensSlider=document.getElementById('sensitivity'),sensVal=document.getElementById('sensVal');
  const rotSlider=document.getElementById('rotSpeed'),rotVal=document.getElementById('rotVal');
  const paletteSel=document.getElementById('palette');
  countSlider.oninput=()=>{countVal.textContent=countSlider.value;spawnVisualizers();}
  barsSlider.oninput=()=>barsVal.textContent=barsSlider.value;
  sensSlider.oninput=()=>sensVal.textContent=parseFloat(sensSlider.value).toFixed(2);
  rotSlider.oninput=()=>rotVal.textContent=parseFloat(rotSlider.value).toFixed(2);

  function getPalette(name){
    switch(name){
      case 'fire':return{bg:'#120200',colors:['#ff9a3c','#ff4040','#ffd07a']};
      case 'green':return{bg:'#001206',colors:['#7CFFB2','#00F27A','#0BB57D']};
      default:return{bg:'#020416',colors:['#6EE7FF','#3A8DFF','#7A3CFF']};
    }
  }
  function hexToRGBA(hex,a=1){
    const h=hex.replace('#','');
    const r=parseInt(h.slice(0,2),16),g=parseInt(h.slice(2,4),16),b=parseInt(h.slice(4,6),16);
    return `rgba(${r},${g},${b},${a})`;
  }

  // Create fractal texture
  const fractalSize=128,fractalCanvas=document.createElement('canvas');
  fractalCanvas.width=fractalSize;fractalCanvas.height=fractalSize;
  const fctx=fractalCanvas.getContext('2d');
  function makeFractal(){
    let img=fctx.createImageData(fractalSize,fractalSize);
    for(let i=0;i<img.data.length;i+=4){
      const x=(i/4)%fractalSize,y=Math.floor((i/4)/fractalSize);
      const v=(Math.sin(x*0.15)+Math.cos(y*0.1)+Math.sin((x+y)*0.07))*0.33;
      const c=Math.floor((v*0.5+0.5)*255);
      img.data[i]=img.data[i+1]=img.data[i+2]=c;img.data[i+3]=255;
    }
    fctx.putImageData(img,0,0);
  }
  makeFractal();

  let audioCtx=null,analyser=null,dataArray=null,sourceNode=null,audioElement=null,micStream=null,isPlaying=false;
  function ensureAudioContext(){if(!audioCtx)audioCtx=new (window.AudioContext||window.webkitAudioContext)();}
  function setupAnalyser(){ensureAudioContext();if(!analyser)analyser=audioCtx.createAnalyser();analyser.fftSize=2048;dataArray=new Uint8Array(analyser.frequencyBinCount);}

  document.getElementById('file').addEventListener('change',async e=>{
    const f=e.target.files[0];if(!f)return;
    stopMic();stopAudioElement();
    audioElement=new Audio(URL.createObjectURL(f));audioElement.loop=true;
    ensureAudioContext();sourceNode=audioCtx.createMediaElementSource(audioElement);
    setupAnalyser();sourceNode.connect(analyser);analyser.connect(audioCtx.destination);
    await audioElement.play();isPlaying=true;
  });
  document.getElementById('play').addEventListener('click',async()=>{
    ensureAudioContext();if(!audioElement)return;
    if(isPlaying){audioElement.pause();isPlaying=false;}
    else{await audioElement.play();isPlaying=true;}
  });
  document.getElementById('mic').addEventListener('click',async()=>{
    if(micStream){stopMic();return;}
    micStream=await navigator.mediaDevices.getUserMedia({audio:true});
    ensureAudioContext();setupAnalyser();
    sourceNode=audioCtx.createMediaStreamSource(micStream);sourceNode.connect(analyser);
  });
  function stopMic(){if(micStream)micStream.getTracks().forEach(t=>t.stop());micStream=null;}
  function stopAudioElement(){if(audioElement){audioElement.pause();audioElement=null;isPlaying=false;}}

  let vizInstances=[];
  function spawnVisualizers(){
    container.innerHTML='';vizInstances=[];
    const count=parseInt(countSlider.value);
    const cols=Math.ceil(Math.sqrt(count));
    container.style.gridTemplateColumns=`repeat(${cols},1fr)`;
    for(let i=0;i<count;i++){
      const c=document.createElement('canvas');c.className='viz';
      container.appendChild(c);
      vizInstances.push({canvas:c,ctx:c.getContext('2d'),rotOffset:Math.random()*Math.PI*2});
    }
  }
  spawnVisualizers();

  function render(now){
    const bars=parseInt(barsSlider.value);
    if(analyser&&dataArray)analyser.getByteFrequencyData(dataArray);
    const spectrum=dataArray?mapSpectrum(dataArray,bars):fakeSpectrum(bars,now);
    const pal=getPalette(paletteSel.value);
    const bass=spectrum.slice(0,Math.max(4,bars*0.1)).reduce((a,b)=>a+b,0)/(bars*0.1*255);
    vizInstances.forEach(inst=>drawVisualizer(inst,now,spectrum,pal,bass));
    requestAnimationFrame(render);
  }
  function mapSpectrum(arr,bars){
    const out=new Uint8Array(bars);
    const factor=Math.floor(arr.length/bars);
    for(let i=0;i<bars;i++){let sum=0;for(let k=0;k<factor;k++)sum+=arr[i*factor+k];out[i]=sum/factor;}
    return out;
  }
  function fakeSpectrum(bars,t){
    const a=new Uint8Array(bars);
    for(let i=0;i<bars;i++){a[i]=Math.max(6,Math.min(255,Math.round((Math.sin(t*0.002+i*0.14)*0.5+0.5)*220)));}return a;
  }

  function drawVisualizer(inst,now,spectrum,pal,bass){
    const c=inst.canvas,ctx=inst.ctx;
    const w=c.width=c.clientWidth,h=c.height=c.clientHeight;
    ctx.setTransform(1,0,0,1,0,0);ctx.clearRect(0,0,w,h);
    const cx=w/2,cy=h*0.62;
    // fractal background
    const zoom=1+0.6*bass;
    ctx.save();ctx.globalAlpha=0.2+0.3*bass;
    ctx.translate(cx,cy);ctx.scale(zoom,zoom);ctx.rotate(now*0.00005);
    ctx.drawImage(fractalCanvas,-w/2,-h/2,w,h);ctx.restore();
    // grid lines
    ctx.save();ctx.translate(cx,cy);
    ctx.strokeStyle=hexToRGBA(pal.colors[0],0.15);
    const t=now*0.001;
    for(let i=0;i<30;i++){
      const angle=(i/30)*Math.PI*2;
      ctx.strokeStyle=hexToRGBA(pal.colors[i%pal.colors.length],0.1+0.15*Math.sin(t+i));
      ctx.beginPath();ctx.moveTo(Math.cos(angle)*10,Math.sin(angle)*10);
      ctx.lineTo(Math.cos(angle)*w,Math.sin(angle)*h);ctx.stroke();
    }
    ctx.restore();
    // ring bars
    ctx.save();ctx.translate(cx,cy);
    const ringR=Math.min(w,h)*0.2;
    inst.rotOffset+=parseFloat(rotSlider.value)*0.016;
    for(let i=0;i<spectrum.length;i++){
      const mag=spectrum[i]/255;
      let hbar=Math.pow(mag,1.2)*320*parseFloat(sensSlider.value);
      const angle=(i/spectrum.length)*Math.PI*2+inst.rotOffset;
      const x=Math.cos(angle)*ringR,z=Math.sin(angle)*ringR;
      const fov=450,perspective=fov/(fov+z);
      if(hbar>(cy-30)/perspective)hbar=(cy-30)/perspective;
      const px=x*perspective,py=-hbar*perspective,sx=10*perspective;
      const grad=ctx.createLinearGradient(px,py,px,py+Math.abs(hbar)*0.6);
      grad.addColorStop(0,hexToRGBA(pal.colors[2]||pal.colors[1],0.95));
      grad.addColorStop(1,'rgba(0,0,0,0)');
      ctx.beginPath();
      ctx.moveTo(px-sx/2,py);ctx.lineTo(px+sx/2,py);
      ctx.lineTo(px+sx/1.7,10*perspective);ctx.lineTo(px-sx/1.7,10*perspective);
      ctx.closePath();ctx.fillStyle=grad;ctx.fill();
    }
    ctx.restore();
  }

  requestAnimationFrame(render);
})();
</script>
</body>
</html>
