<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<title>Sphere Music Visualizer</title>
<style>
  body { margin: 0; overflow: hidden; background: #000; }
  canvas { display: block; }
</style>
</head>
<body>
<canvas id="visualizer"></canvas>
<input type="file" id="audiofile" accept="audio/*" style="position:absolute;top:10px;left:10px;z-index:10;">
<script>
const canvas = document.getElementById('visualizer');
const ctx = canvas.getContext('2d');
let width = canvas.width = window.innerWidth;
let height = canvas.height = window.innerHeight;

window.addEventListener('resize', () => {
    width = canvas.width = window.innerWidth;
    height = canvas.height = window.innerHeight;
});

let audioCtx, analyser, source, dataArray, bufferLength;
let audioLoaded = false;

document.getElementById('audiofile').addEventListener('change', function(e){
    const file = e.target.files[0];
    if (!file) return;
    const reader = new FileReader();
    reader.onload = function(ev) {
        if (audioCtx) audioCtx.close();
        audioCtx = new (window.AudioContext || window.webkitAudioContext)();
        const audio = new Audio(ev.target.result);
        audio.loop = true;
        audio.play();
        source = audioCtx.createMediaElementSource(audio);
        analyser = audioCtx.createAnalyser();
        source.connect(analyser);
        analyser.connect(audioCtx.destination);
        analyser.fftSize = 512;
        bufferLength = analyser.frequencyBinCount;
        dataArray = new Uint8Array(bufferLength);
        audioLoaded = true;
    }
    reader.readAsDataURL(file);
});

// Sphere setup
const nodes = [];
const NODE_COUNT = 400;
const sphereRadius = Math.min(width, height) / 4;

for (let i = 0; i < NODE_COUNT; i++) {
    const theta = Math.random() * Math.PI;
    const phi = Math.random() * 2 * Math.PI;
    nodes.push({
        theta,
        phi,
        x: 0,
        y: 0,
        z: 0,
        prevX: 0,
        prevY: 0
    });
}

let rotationY = 0;

function animate() {
    ctx.fillStyle = 'rgba(0,0,0,0.1)'; // tracer effect
    ctx.fillRect(0, 0, width, height);

    if (audioLoaded) analyser.getByteFrequencyData(dataArray);

    rotationY += 0.002;

    nodes.forEach((node, i) => {
        // Convert spherical to Cartesian
        let r = sphereRadius;
        if (audioLoaded) r += (dataArray[i % bufferLength] / 255) * 100; // radius reacts to music

        const x = r * Math.sin(node.theta) * Math.cos(node.phi + rotationY);
        const y = r * Math.cos(node.theta);
        const z = r * Math.sin(node.theta) * Math.sin(node.phi + rotationY);

        // Simple perspective projection
        const scale = 300 / (300 + z);
        const projX = width/2 + x * scale;
        const projY = height/2 + y * scale;

        // Tracer effect
        ctx.strokeStyle = `hsl(${i*2}, 100%, 50%)`;
        ctx.beginPath();
        ctx.moveTo(node.prevX || projX, node.prevY || projY);
        ctx.lineTo(projX, projY);
        ctx.stroke();

        node.prevX = projX;
        node.prevY = projY;

        // Draw node as pixel
        ctx.fillStyle = `hsl(${i*2}, 100%, 50%)`;
        ctx.fillRect(projX, projY, 2, 2);
    });

    requestAnimationFrame(animate);
}

animate();
</script>
</body>
</html>
