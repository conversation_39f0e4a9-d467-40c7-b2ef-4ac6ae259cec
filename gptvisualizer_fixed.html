<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8" />
<meta name="viewport" content="width=device-width,initial-scale=1" />
<title>Visualizer — Fixed / Holographic Spacetime Grid</title>
<style>
  :root{--bg:#05060b;--panel:rgba(10,12,18,0.6);--text:#dfefff}
  html,body{height:100%;margin:0;background: radial-gradient(1200px 600px at 10% 10%, #081025 0%, transparent 10%), var(--bg); font-family:Inter,system-ui,Arial; color:var(--text); overflow:hidden}
  canvas{display:block;width:100%;height:100%}
  .ui{position: absolute; left: 14px; top: 14px; z-index:20; background:var(--panel); padding:10px; border-radius:10px; backdrop-filter: blur(6px); width:360px}
  .ui h1{font-size:15px;margin:0 0 8px 0}
  .row{display:flex;gap:8px;align-items:center;margin:6px 0}
  label{font-size:12px;opacity:0.9;width:70px}
  input[type=range]{width:150px}
  button,input[type=file],select{background:transparent;border:1px solid rgba(255,255,255,0.08);color:var(--text);padding:6px;border-radius:8px;cursor:pointer}
  .status{position:absolute;right:14px;top:14px;background:var(--panel);padding:8px;border-radius:10px;z-index:20;min-width:220px}
  .status .line{font-size:12px;margin:4px 0;color:#bfe}
  .log{margin-top:8px;max-height:120px;overflow:auto;font-size:11px;background:rgba(0,0,0,0.25);padding:6px;border-radius:6px}
  .hint{font-size:12px;opacity:0.85;margin-top:6px}
  .footer{position:absolute;left:50%;transform:translateX(-50%);bottom:12px;color:rgba(255,255,255,0.6);font-size:12px;padding:6px 10px;border-radius:8px;background:rgba(0,0,0,0.2)}
</style>
</head>
<body>
<canvas id="c"></canvas>

<div class="ui">
  <h1>3D Ring Visualizer — Fixed + Spacetime Grid</h1>
  <div class="row">
    <label>Audio</label>
    <input id="file" type="file" accept="audio/*">
    <button id="play">Play</button>
    <button id="mic">Mic</button>
  </div>

  <div class="row">
    <label>Bars</label>
    <input id="bars" type="range" min="16" max="256" value="128">
    <div id="barsVal">128</div>
  </div>

  <div class="row">
    <label>Sens</label>
    <input id="sensitivity" type="range" min="0.3" max="6" step="0.05" value="1.6">
    <div id="sensVal">1.60</div>
  </div>

  <div class="row">
    <label>Rotate</label>
    <input id="rotSpeed" type="range" min="-1.5" max="1.5" step="0.01" value="0.15">
    <div id="rotVal">0.15</div>
  </div>

  <div class="row">
    <label>Palette</label>
    <select id="palette"><option value="neon">Neon</option><option value="fire">Fire</option><option value="green">Green</option></select>
  </div>

  <div class="hint">Tip: Click anywhere on the page to enable audio (required by some browsers). If using mic, run on HTTPS or localhost.</div>
</div>

<div class="status" id="statusBox">
  <div class="line" id="audioCtxStatus">AudioContext: —</div>
  <div class="line" id="inputStatus">Input: none</div>
  <div class="line" id="analyserStatus">Analyser: —</div>
  <div class="line" id="fps">FPS: —</div>
  <div class="log" id="log"></div>
</div>

<div class="footer">If something looks wrong, open DevTools (Ctrl+Shift+J) and paste any errors into chat.</div>

<script>
(() => {
  const canvas = document.getElementById('c');
  const ctx = canvas.getContext('2d', { alpha: true });
  const fileInput = document.getElementById('file');
  const playBtn = document.getElementById('play');
  const micBtn = document.getElementById('mic');
  const barsSlider = document.getElementById('bars');
  const barsVal = document.getElementById('barsVal');
  const sensSlider = document.getElementById('sensitivity');
  const sensVal = document.getElementById('sensVal');
  const rotSlider = document.getElementById('rotSpeed');
  const rotVal = document.getElementById('rotVal');
  const paletteSel = document.getElementById('palette');

  const audioCtxStatus = document.getElementById('audioCtxStatus');
  const inputStatus = document.getElementById('inputStatus');
  const analyserStatus = document.getElementById('analyserStatus');
  const fpsEl = document.getElementById('fps');
  const logEl = document.getElementById('log');

  // sizing
  function fit() {
    const dpr = window.devicePixelRatio || 1;
    canvas.width = innerWidth * dpr;
    canvas.height = innerHeight * dpr;
    canvas.style.width = innerWidth + 'px';
    canvas.style.height = innerHeight + 'px';
    ctx.setTransform(dpr,0,0,dpr,0,0);
  }
  addEventListener('resize', fit);
  fit();

  // audio
  let audioCtx = null;
  let analyser = null;
  let dataArray = null;
  let bufferLength = 1024; // fallback
  let sourceNode = null;
  let audioElement = null;
  let micStream = null;
  let isPlaying = false;
  let demoMode = true; // show demo when no audio
  let lastTime = performance.now();
  let perf = { lastFps: performance.now(), frames:0, fps:0 };

  function log(msg) {
    const el = document.createElement('div'); el.textContent = msg;
    logEl.appendChild(el);
    logEl.scrollTop = logEl.scrollHeight;
    console.log('VIZ:', msg);
  }

  function ensureAudioContext() {
    if (!audioCtx) {
      try {
        audioCtx = new (window.AudioContext || window.webkitAudioContext)();
        audioCtxStatus.textContent = 'AudioContext: created (' + (audioCtx.state||'—') + ')';
      } catch (e) {
        audioCtxStatus.textContent = 'AudioContext: unavailable';
        log('AudioContext creation failed: ' + e);
      }
    } else {
      audioCtxStatus.textContent = 'AudioContext: ' + audioCtx.state;
    }
  }

  function setupAnalyser(fft=2048) {
    ensureAudioContext();
    if (!audioCtx) return;
    if (!analyser) analyser = audioCtx.createAnalyser();
    analyser.fftSize = fft;
    bufferLength = analyser.frequencyBinCount;
    dataArray = new Uint8Array(bufferLength);
    analyserStatus.textContent = `Analyser: ready (fft=${fft}, bins=${bufferLength})`;
    log('Analyser ready');
  }

  // status updates
  function setInputText(s) { inputStatus.textContent = 'Input: ' + s; }

  // file handling
  fileInput.addEventListener('change', async (e) => {
    const f = e.target.files && e.target.files[0];
    if (!f) { log('No file selected'); return; }
    stopMic();
    stopAudioElement();
    try {
      const url = URL.createObjectURL(f);
      audioElement = new Audio(url);
      audioElement.crossOrigin = 'anonymous';
      audioElement.loop = true;
      ensureAudioContext();
      if (!audioCtx) { log('No audio context'); return; }
      try { sourceNode && sourceNode.disconnect(); } catch(_) {}
      sourceNode = audioCtx.createMediaElementSource(audioElement);
      setupAnalyser(2048);
      sourceNode.connect(analyser);
      analyser.connect(audioCtx.destination);
      await audioElement.play();
      isPlaying = true;
      demoMode = false;
      playBtn.textContent = 'Pause';
      setInputText('file: ' + f.name);
      log('Playing file: ' + f.name);
    } catch (err) {
      log('File play error: ' + err);
      setInputText('file: (error)');
    }
  });

  // play / demo toggle
  playBtn.addEventListener('click', async () => {
    ensureAudioContext();
    if (!audioElement) {
      // toggle demo visual (no audio)
      demoMode = !demoMode;
      playBtn.textContent = demoMode ? 'Demo (on)' : 'Demo (off)';
      setInputText(demoMode ? 'demo' : 'none');
      return;
    }
    if (isPlaying) {
      audioElement.pause(); isPlaying = false; playBtn.textContent = 'Play'; setInputText('file (paused)');
    } else {
      await audioElement.play().catch(e=>log('Play rejected: '+e));
      isPlaying = true; playBtn.textContent = 'Pause'; setInputText('file (playing)');
    }
  });

  // mic
  micBtn.addEventListener('click', async () => {
    if (micStream) { stopMic(); micBtn.textContent = 'Mic'; setInputText('none'); return; }
    if (location.protocol !== 'https:' && location.hostname !== 'localhost' && location.protocol !== 'file:') {
      log('Microphone requires HTTPS or localhost. Current protocol: ' + location.protocol);
    }
    try {
      micStream = await navigator.mediaDevices.getUserMedia({ audio:true });
      ensureAudioContext();
      setupAnalyser(2048);
      try { sourceNode && sourceNode.disconnect(); } catch(_) {}
      sourceNode = audioCtx.createMediaStreamSource(micStream);
      sourceNode.connect(analyser);
      micBtn.textContent = 'Mic (on)';
      demoMode = false;
      setInputText('mic');
      log('Mic stream started');
    } catch (err) {
      log('Mic error: ' + err);
    }
  });

  function stopMic() {
    if (!micStream) return;
    try { micStream.getTracks().forEach(t=>t.stop()); } catch(_) {}
    micStream = null;
    try { if (sourceNode) sourceNode.disconnect(); } catch(_) {}
    log('Mic stopped');
  }

  function stopAudioElement() {
    if (audioElement) {
      try { audioElement.pause(); audioElement = null; } catch(_) {}
      isPlaying = false;
    }
  }

  // resume audio on any user gesture (required by browsers)
  function resumeOnGesture() {
    const resume = async () => {
      if (audioCtx && audioCtx.state === 'suspended') {
        try { await audioCtx.resume(); audioCtxStatus.textContent = 'AudioContext: ' + audioCtx.state; log('AudioContext resumed'); }
        catch(e){ log('Resume failed: '+e); }
      }
      window.removeEventListener('pointerdown', resume);
      window.removeEventListener('keydown', resume);
    };
    window.addEventListener('pointerdown', resume);
    window.addEventListener('keydown', resume);
  }
  resumeOnGesture();

  // palettes
  function getPalette(name) {
    switch(name) {
      case 'fire': return { bg:'#120200', colors:['#ff9a3c','#ff4040','#ffd07a'] };
      case 'green': return { bg:'#001206', colors:['#7CFFB2','#00F27A','#0BB57D'] };
      default: return { bg:'#020416', colors:['#6EE7FF','#3A8DFF','#7A3CFF'] };
    }
  }

  // fake spectrum generator (so visuals show without input)
  function generateFakeSpectrum(bars, t) {
    const arr = new Uint8Array(bars);
    for (let i=0;i<bars;i++) {
      // layered sines with per-index offset + slow envelope
      const a = Math.sin(t*0.002 + i*0.14) * 0.5 + 0.5;
      const b = Math.sin(t*0.006 + i*0.08 + Math.cos(i)) * 0.5 + 0.5;
      const low = Math.max(0, Math.sin(t*0.001 + i*0.05) * 0.5 + 0.5);
      const v = (0.6*a + 0.35*b + 0.2*low) * 220 * (0.6 + 0.4*Math.random());
      arr[i] = Math.max(6, Math.min(255, Math.round(v)));
    }
    return arr;
  }

  // helpers
  function hexToRGBA(hex, a=1) {
    const h = hex.replace('#','');
    const r = parseInt(h.slice(0,2),16), g = parseInt(h.slice(2,4),16), b = parseInt(h.slice(4,6),16);
    return `rgba(${r},${g},${b},${a})`;
  }

  // UI reflecting values
  barsSlider.addEventListener('input', ()=>{ barsVal.textContent = barsSlider.value; });
  sensSlider.addEventListener('input', ()=>{ sensVal.textContent = parseFloat(sensSlider.value).toFixed(2); });
  rotSlider.addEventListener('input', ()=>{ rotVal.textContent = parseFloat(rotSlider.value).toFixed(2); });

  // render loop
  let last = performance.now();
  let ringRot = 0;

  // GRID: holographic spacetime grid function
  function drawSpacetimeGrid(now, pal, cx, cy) {
    // background grid (soft, slow)
    ctx.save();
    ctx.globalCompositeOperation = 'lighter';
    ctx.lineWidth = 1;
    const t = now * 0.0009;
    // background faint radial lines
    ctx.globalAlpha = 0.18;
    for (let i=0;i<28;i++) {
      const angle = (i / 28) * Math.PI * 2 + t*0.2;
      ctx.beginPath();
      ctx.moveTo(cx + Math.cos(angle) * 20, cy - innerHeight*0.45 + Math.sin(angle)*20);
      ctx.lineTo(cx + Math.cos(angle) * (innerWidth), cy + Math.sin(angle) * (innerHeight));
      ctx.strokeStyle = hexToRGBA(pal.colors[0], 0.06 + 0.12*(i%3));
      ctx.stroke();
    }
    ctx.globalAlpha = 1;

    // floor grid — zooming perspective grid
    ctx.save();
    ctx.translate(cx, cy);
    ctx.globalCompositeOperation = 'lighter';
    const speed = (t % 1) * 0.75;
    const rows = 40;
    for (let i=1;i<=rows;i++) {
      const depth = i + speed * 6;
      const perspective = 700 / (700 + depth * 40); // smaller -> near, larger -> far
      const y = (200 + i*18) * (1 - perspective); // pushes lines down for near, up for far
      const lineWidth = Math.max(0.4, 3 * perspective * (1 + (i%5)*0.06));
      ctx.lineWidth = lineWidth;
      const alpha = Math.max(0.03, 0.45 * perspective * (1 - i/rows));
      const col = hexToRGBA(pal.colors[1], alpha * 0.9);
      ctx.strokeStyle = col;
      ctx.beginPath();
      // horizontal-ish rings (approximated by flattened ellipses)
      const w = innerWidth * (1.05 - perspective*0.9);
      ctx.ellipse(0, y + 40, w*0.5, Math.max(1, 18 * perspective), 0, 0, Math.PI*2);
      ctx.stroke();
    }

    // vertical grid lines (radial lines converging to horizon)
    ctx.lineWidth = 1;
    const spokes = 36;
    for (let s=0;s<spokes;s++) {
      const a = (s/spokes) * Math.PI*2 + t*0.12;
      ctx.beginPath();
      ctx.moveTo(Math.cos(a) * 10, 0);
      ctx.lineTo(Math.cos(a) * (innerWidth*0.6), innerHeight);
      ctx.strokeStyle = hexToRGBA(pal.colors[2] || pal.colors[1], 0.06);
      ctx.stroke();
    }

    ctx.restore();
    ctx.restore();
  }

  function render(now) {
    const dt = Math.min(0.05, (now - last)/1000);
    last = now;
    perf.frames++;
    if (now - perf.lastFps >= 500) {
      perf.fps = Math.round((perf.frames*1000) / (now - perf.lastFps));
      perf.frames = 0; perf.lastFps = now;
      fpsEl.textContent = 'FPS: ' + perf.fps;
    }

    // canvas clear background
    const pal = getPalette(paletteSel.value);
    ctx.clearRect(0,0,innerWidth,innerHeight);
    const g = ctx.createLinearGradient(0,0,0,innerHeight);
    g.addColorStop(0,pal.bg);
    g.addColorStop(1,'rgba(0,0,0,0.12)');
    ctx.fillStyle = g; ctx.fillRect(0,0,innerWidth,innerHeight);

    // draw holographic spacetime grid
    const cx = innerWidth/2;
    // push ring lower on screen so bars have room above
    const cy = innerHeight * 0.62;
    drawSpacetimeGrid(now, pal, cx, cy);

    // get spectrum data (map analyser bins -> bars)
    const bars = parseInt(barsSlider.value,10);
    let spectrum = null;
    if (analyser && dataArray) {
      try {
        analyser.getByteFrequencyData(dataArray);
        // map to 'bars' length by averaging bins
        spectrum = new Uint8Array(bars);
        const factor = Math.max(1, Math.floor(dataArray.length / bars));
        for (let i=0;i<bars;i++) {
          let sum = 0, count = 0;
          const start = i*factor;
          for (let k=0;k<factor && (start+k)<dataArray.length;k++){ sum += dataArray[start+k]; count++; }
          spectrum[i] = count ? Math.round(sum/count) : 0;
        }
      } catch (e) {
        log('Analyser read error: ' + e);
        spectrum = generateFakeSpectrum(bars, now);
      }
    } else {
      // no analyser => use demo
      spectrum = generateFakeSpectrum(bars, now);
    }

    // draw ring bars (simplified, robust)
    const ringR = Math.min(innerWidth,innerHeight) * 0.20;
    ringRot += parseFloat(rotSlider.value) * dt;

    ctx.save(); ctx.translate(cx,cy); ctx.globalCompositeOperation = 'lighter';
    for (let i=0;i<bars;i++) {
      const mag = spectrum[i] / 255;
      const shape = 1 - Math.abs((i/bars) - 0.25) * 1.3;
      let h = Math.pow(mag,1.2) * 320 * parseFloat(sensSlider.value) * Math.max(0.2, shape);
      const angle = (i/bars) * Math.PI*2 + ringRot;
      const x = Math.cos(angle) * (ringR + 20 * (spectrum[0]/255));
      const z = Math.sin(angle) * (ringR + 20 * (spectrum[0]/255));
      const fov = 450;
      const perspective = fov / (fov + z);

      // --- SAFE TOP CLAMP: ensure bar top doesn't go above 30px from top of canvas ---
      // compute maximum allowed height so that cy + (-h*perspective) >= 30
      const maxTop = 30; // pixels from top
      const maxH = Math.max(6, (cy - maxTop) / Math.max(0.0001, perspective));
      if (h > maxH) h = maxH;

      const px = x * perspective;
      const py = -h * perspective;
      const bx = px, by = py;
      const sx = (10 * perspective) * (1 + 0.6*(1-perspective));
      // colors
      const colors = pal.colors; const mix = Math.min(1, mag * 1.2);
      const grad = ctx.createLinearGradient(bx, by, bx, by + Math.abs(h)*0.6);
      grad.addColorStop(0, hexToRGBA(colors[2]||colors[1]||colors[0], 0.95));
      grad.addColorStop(0.35, hexToRGBA(colors[1]||colors[0], 0.9));
      grad.addColorStop(1, 'rgba(0,0,0,0.3)');
      // draw trapezoid
      ctx.beginPath();
      const xTop = bx, yTop = by;
      const xBot = bx, yBot = 10 * perspective;
      ctx.moveTo(xTop - sx/2, yTop);
      ctx.lineTo(xTop + sx/2, yTop);
      ctx.lineTo(xBot + sx/1.7, yBot);
      ctx.lineTo(xBot - sx/1.7, yBot);
      ctx.closePath();
      ctx.fillStyle = grad; ctx.fill();
      // highlight
      ctx.strokeStyle = hexToRGBA('#ffffff', 0.06 + 0.4 * mix);
      ctx.lineWidth = Math.max(0.3, 1.2 * perspective);
      ctx.stroke();
      // cap
      ctx.beginPath();
      ctx.ellipse(xTop, yTop, Math.max(1, sx*0.6), Math.max(1, sx*0.14), 0, 0, Math.PI*2);
      const capg = ctx.createRadialGradient(xTop,yTop,0,xTop,yTop,Math.max(8,sx*6));
      capg.addColorStop(0, hexToRGBA(colors[0], 0.9*mix));
      capg.addColorStop(1, 'rgba(0,0,0,0)');
      ctx.fillStyle = capg; ctx.fill();
    }
    ctx.restore();

    requestAnimationFrame(render);
  }
  requestAnimationFrame(render);

  // small periodic status refresh
  setInterval(()=> {
    audioCtxStatus.textContent = 'AudioContext: ' + (audioCtx ? (audioCtx.state||'—') : 'not created');
    analyserStatus.textContent = 'Analyser: ' + (analyser ? ('ready bins=' + bufferLength) : 'no analyser');
  }, 700);

  // helpful: detect errors globally
  window.addEventListener('error', (e)=>{ log('Window error: ' + (e.message || e)); });
  window.addEventListener('unhandledrejection', (e)=>{ log('Unhandled promise rejection: ' + e.reason); });

  // initial messages
  log('Visualizer (fixed + grid) loaded. Click the page to enable audio if needed.');
})();
</script>
</body>
</html>
