<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<title>NCS Orb Waveform Visualizer</title>
<style>
html, body { margin:0; background:black; overflow:hidden; }
canvas { display:block; }
#controls { position:fixed; top:10px; left:10px; z-index:10; color:white; font-family:sans-serif; }
</style>
</head>
<body>
<div id="controls">
    <label>Waveform: 
        <select id="waveType">
            <option value="sine">Sine</option>
            <option value="triangle">Triangle</option>
            <option value="saw">Saw</option>
            <option value="supersaw">Super Saw</option>
        </select>
    </label>
    <input type="file" id="audioInput" accept="audio/*">
</div>
<canvas id="visualizer"></canvas>
<script>
const canvas = document.getElementById("visualizer");
const ctx = canvas.getContext("2d");
let width, height;
function resize(){ width=canvas.width=window.innerWidth; height=canvas.height=window.innerHeight; }
window.addEventListener("resize", resize);
resize();

const waveSelect = document.getElementById("waveType");
let currentWave = waveSelect.value;
waveSelect.addEventListener("change",()=>currentWave=waveSelect.value);

// Audio setup
let audioCtx, analyser, dataArray;
document.getElementById("audioInput").addEventListener("change", async e=>{
    const file = e.target.files[0]; if(!file) return;
    if(audioCtx) audioCtx.close();
    audioCtx = new (window.AudioContext||window.webkitAudioContext)();
    const buffer = await file.arrayBuffer();
    const audioBuffer = await audioCtx.decodeAudioData(buffer);
    const track = audioCtx.createBufferSource();
    track.buffer = audioBuffer;
    analyser = audioCtx.createAnalyser();
    analyser.fftSize = 1024;
    dataArray = new Uint8Array(analyser.frequencyBinCount);
    track.connect(analyser);
    analyser.connect(audioCtx.destination);
    track.start();
});

// --- Fibonacci Sphere (orb) ---
const nodeCount = 1400;
const nodes = [];
const goldenAngle = Math.PI*(3-Math.sqrt(5));
for(let i=0;i<nodeCount;i++){
    const y = 1-(i/(nodeCount-1))*2;
    const radius = Math.sqrt(1-y*y);
    const theta = goldenAngle*i;
    nodes.push({x:Math.cos(theta)*radius, y:y, z:Math.sin(theta)*radius, phi: Math.acos(y)});
}

let rotation=0;

// --- Waveform functions ---
function waveform(value, type){
    switch(type){
        case "sine": return Math.sin(value*Math.PI*2);
        case "triangle": return 2*Math.abs(2*(value%1)-1)-1;
        case "saw": return 2*(value%1)-1;
        case "supersaw": {
            let sum=0;
            const detune = [0,0.02,-0.02,0.04,-0.04];
            for(let d of detune) sum += 2*((value+d)%1)-1;
            return sum/detune.length;
        }
    }
    return 0;
}

function animate(){
    requestAnimationFrame(animate);

    ctx.fillStyle="black";
    ctx.fillRect(0,0,width,height);

    let bass=0, mids=0;
    if(analyser){
        analyser.getByteFrequencyData(dataArray);
        bass = dataArray.slice(0,8).reduce((a,b)=>a+b,0)/(8*255);
        mids = dataArray.slice(8,60).reduce((a,b)=>a+b,0)/(52*255);
    }

    rotation+=0.002;
    const cx = width/2, cy = height/2;
    const baseRadius = Math.min(width,height)*0.25;
    const time = performance.now()/1000;

    for(let node of nodes){
        // calculate wave phase based on node position
        let phase = (node.phi + time*0.5) % 1;

        // displacement using waveform type
        let disp = waveform(phase, currentWave) * (0.1 + bass*0.2);

        let x = node.x*(1+disp);
        let y = node.y*(1+disp);
        let z = node.z*(1+disp);

        const rotX = x*Math.cos(rotation)-z*Math.sin(rotation);
        const rotZ = x*Math.sin(rotation)+z*Math.cos(rotation);
        const perspective = 1/(1-rotZ*0.4);
        const x2d = rotX*baseRadius*perspective;
        const y2d = y*baseRadius*perspective;

        ctx.beginPath();
        ctx.arc(cx+x2d, cy+y2d, 1.6, 0, Math.PI*2);
        ctx.fillStyle = "hsl(180,100%,60%)";
        ctx.fill();
    }
}

animate();
</script>
</body>
</html>
