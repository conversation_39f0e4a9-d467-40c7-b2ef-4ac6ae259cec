<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8" />
<meta name="viewport" content="width=device-width,initial-scale=1" />
<title>Visualizer — Color Grid + Music-Reactive Fractal</title>
<style>
  :root{--bg:#05060b;--panel:rgba(10,12,18,0.6);--text:#dfefff}
  html,body{height:100%;margin:0;background:var(--bg); font-family:Inter,system-ui,Arial; color:var(--text); overflow:hidden}
  canvas{display:block;width:100%;height:100%}
  .ui{position:absolute;left:14px;top:14px;z-index:20;background:var(--panel);padding:10px;border-radius:10px;backdrop-filter:blur(6px);width:360px}
  .ui h1{font-size:15px;margin:0 0 8px 0}
  .row{display:flex;gap:8px;align-items:center;margin:6px 0}
  label{font-size:12px;opacity:0.9;width:70px}
  input[type=range]{width:150px}
  button,input[type=file],select{background:transparent;border:1px solid rgba(255,255,255,0.08);color:var(--text);padding:6px;border-radius:8px;cursor:pointer}
  .status{position:absolute;right:14px;top:14px;background:var(--panel);padding:8px;border-radius:10px;z-index:20;min-width:220px}
  .status .line{font-size:12px;margin:4px 0;color:#bfe}
  .log{margin-top:8px;max-height:120px;overflow:auto;font-size:11px;background:rgba(0,0,0,0.25);padding:6px;border-radius:6px}
  .hint{font-size:12px;opacity:0.85;margin-top:6px}
  .footer{position:absolute;left:50%;transform:translateX(-50%);bottom:12px;color:rgba(255,255,255,0.6);font-size:12px;padding:6px 10px;border-radius:8px;background:rgba(0,0,0,0.2)}
</style>
</head>
<body>
<canvas id="c"></canvas>
<div class="ui">
  <h1>Visualizer — Color Grid + Fractal</h1>
  <div class="row">
    <label>Audio</label><input id="file" type="file" accept="audio/*">
    <button id="play">Play</button><button id="mic">Mic</button>
  </div>
  <div class="row"><label>Bars</label><input id="bars" type="range" min="16" max="256" value="128"><div id="barsVal">128</div></div>
  <div class="row"><label>Sens</label><input id="sensitivity" type="range" min="0.3" max="6" step="0.05" value="1.6"><div id="sensVal">1.60</div></div>
  <div class="row"><label>Rotate</label><input id="rotSpeed" type="range" min="-1.5" max="1.5" step="0.01" value="0.15"><div id="rotVal">0.15</div></div>
  <div class="row"><label>Palette</label><select id="palette"><option value="neon">Neon</option><option value="fire">Fire</option><option value="green">Green</option></select></div>
</div>
<div class="status" id="statusBox">
  <div class="line" id="audioCtxStatus">AudioContext: —</div>
  <div class="line" id="inputStatus">Input: none</div>
  <div class="line" id="analyserStatus">Analyser: —</div>
  <div class="line" id="fps">FPS: —</div>
  <div class="log" id="log"></div>
</div>
<div class="footer">Fractal reacts to bass — try some heavy music!</div>

<script>
(() => {
  const canvas = document.getElementById('c');
  const ctx = canvas.getContext('2d', { alpha: true });
  function fit(){const dpr=window.devicePixelRatio||1;canvas.width=innerWidth*dpr;canvas.height=innerHeight*dpr;ctx.setTransform(dpr,0,0,dpr,0,0);}window.addEventListener('resize',fit);fit();

  let audioCtx=null,analyser=null,dataArray=null,bufferLength=1024,sourceNode=null,audioElement=null,micStream=null;
  let spectrum=null,isPlaying=false,demoMode=true;

  function ensureAudioContext(){if(!audioCtx)audioCtx=new (window.AudioContext||window.webkitAudioContext)();}
  function setupAnalyser(fft=2048){ensureAudioContext();if(!analyser)analyser=audioCtx.createAnalyser();analyser.fftSize=fft;bufferLength=analyser.frequencyBinCount;dataArray=new Uint8Array(bufferLength);}

  document.getElementById('file').addEventListener('change',async e=>{
    const f=e.target.files[0];if(!f)return;
    stopMic();stopAudioElement();
    const url=URL.createObjectURL(f);audioElement=new Audio(url);audioElement.crossOrigin='anonymous';audioElement.loop=true;
    ensureAudioContext();sourceNode=audioCtx.createMediaElementSource(audioElement);setupAnalyser(2048);sourceNode.connect(analyser);analyser.connect(audioCtx.destination);
    await audioElement.play();isPlaying=true;demoMode=false;
  });
  document.getElementById('play').addEventListener('click',async()=>{
    ensureAudioContext();
    if(!audioElement){demoMode=!demoMode;return;}
    if(isPlaying){audioElement.pause();isPlaying=false;}else{await audioElement.play();isPlaying=true;}
  });
  document.getElementById('mic').addEventListener('click',async()=>{
    if(micStream){stopMic();return;}
    micStream=await navigator.mediaDevices.getUserMedia({audio:true});
    ensureAudioContext();setupAnalyser(2048);sourceNode=audioCtx.createMediaStreamSource(micStream);sourceNode.connect(analyser);demoMode=false;
  });
  function stopMic(){if(!micStream)return;micStream.getTracks().forEach(t=>t.stop());micStream=null;}
  function stopAudioElement(){if(audioElement){audioElement.pause();audioElement=null;isPlaying=false;}}

  function generateFakeSpectrum(bars,t){const arr=new Uint8Array(bars);for(let i=0;i<bars;i++){arr[i]=Math.max(6,Math.min(255,Math.round((Math.sin(t*0.002+i*0.14)*0.5+0.5)*220)));}return arr;}
  function getPalette(name){switch(name){case'fire':return{bg:'#120200',colors:['#ff9a3c','#ff4040','#ffd07a']};case'green':return{bg:'#001206',colors:['#7CFFB2','#00F27A','#0BB57D']};default:return{bg:'#020416',colors:['#6EE7FF','#3A8DFF','#7A3CFF']};}}
  function hexToRGBA(hex,a=1){const h=hex.replace('#','');const r=parseInt(h.slice(0,2),16),g=parseInt(h.slice(2,4),16),b=parseInt(h.slice(4,6),16);return`rgba(${r},${g},${b},${a})`;}

  // Simple fractal generator (precomputed low-res)
  const fractalSize=128;
  let fractalCanvas=document.createElement('canvas');fractalCanvas.width=fractalSize;fractalCanvas.height=fractalSize;
  let fctx=fractalCanvas.getContext('2d');
  function makeFractal(){let img=fctx.createImageData(fractalSize,fractalSize);for(let i=0;i<img.data.length;i+=4){const x=(i/4)%fractalSize,y=Math.floor((i/4)/fractalSize);const v=(Math.sin(x*0.15)+Math.cos(y*0.1)+Math.sin((x+y)*0.07))*0.33;const c=Math.floor((v*0.5+0.5)*255);img.data[i]=img.data[i+1]=img.data[i+2]=c;img.data[i+3]=255;}fctx.putImageData(img,0,0);}makeFractal();

  let last=performance.now(),ringRot=0;
  function drawColorGrid(now,pal,cx,cy,bass){
    ctx.save();ctx.translate(cx,cy);
    const t=now*0.001;
    // radial lines
    const lines=32;
    for(let i=0;i<lines;i++){
      const angle=(i/lines)*Math.PI*2;
      ctx.strokeStyle=hexToRGBA(pal.colors[i%pal.colors.length],0.15+0.2*Math.sin(t+i));
      ctx.beginPath();
      ctx.moveTo(Math.cos(angle)*10,Math.sin(angle)*10);
      ctx.lineTo(Math.cos(angle)*innerWidth,Math.sin(angle)*innerHeight);
      ctx.stroke();
    }
    // floor rings
    const rows=30;
    for(let i=0;i<rows;i++){
      const depth=i+((now*0.002+ bass*0.02)%1)*5;
      const perspective=700/(700+depth*40);
      const y=(200+i*18)*(1-perspective);
      ctx.strokeStyle=hexToRGBA(pal.colors[i%pal.colors.length],0.08+0.2*perspective);
      ctx.beginPath();ctx.ellipse(0,y+40,innerWidth*0.4,18*perspective,0,0,Math.PI*2);ctx.stroke();
    }
    ctx.restore();
  }

  function render(now){
    const dt=(now-last)/1000;last=now;
    const pal=getPalette(document.getElementById('palette').value);
    ctx.clearRect(0,0,innerWidth,innerHeight);
    const bars=parseInt(document.getElementById('bars').value,10);
    if(analyser&&dataArray){analyser.getByteFrequencyData(dataArray);spectrum=new Uint8Array(bars);const factor=Math.floor(dataArray.length/bars);for(let i=0;i<bars;i++){let sum=0;for(let k=0;k<factor;k++){sum+=dataArray[i*factor+k];}spectrum[i]=sum/factor;}}else{spectrum=generateFakeSpectrum(bars,now);}
    const bass=spectrum.slice(0,Math.max(4,bars*0.1)).reduce((a,b)=>a+b,0)/(bars*0.1*255);

    // draw fractal background (scaled + pulsing with bass)
    const zoom=1+0.6*bass;ctx.save();ctx.globalAlpha=0.18+0.35*bass;
    ctx.translate(innerWidth/2,innerHeight/2);ctx.scale(zoom,zoom);ctx.rotate(now*0.00005+0.1*bass);
    ctx.drawImage(fractalCanvas,-innerWidth/2,-innerHeight/2,innerWidth,innerHeight);
    ctx.restore();

    const cx=innerWidth/2,cy=innerHeight*0.62;
    drawColorGrid(now,pal,cx,cy,bass);

    // draw bars
    ringRot+=parseFloat(document.getElementById('rotSpeed').value)*dt;
    ctx.save();ctx.translate(cx,cy);ctx.globalCompositeOperation='lighter';
    const ringR=Math.min(innerWidth,innerHeight)*0.2;
    for(let i=0;i<bars;i++){
      const mag=spectrum[i]/255;let h=Math.pow(mag,1.2)*320*parseFloat(document.getElementById('sensitivity').value);
      const angle=(i/bars)*Math.PI*2+ringRot;const x=Math.cos(angle)*ringR,z=Math.sin(angle)*ringR;const fov=450;const perspective=fov/(fov+z);
      const maxH=(cy-30)/Math.max(0.0001,perspective);if(h>maxH)h=maxH;
      const px=x*perspective,py=-h*perspective,sx=10*perspective;
      const grad=ctx.createLinearGradient(px,py,px,py+Math.abs(h)*0.6);grad.addColorStop(0,hexToRGBA(pal.colors[2]||pal.colors[1],0.95));grad.addColorStop(1,'rgba(0,0,0,0)');
      ctx.beginPath();ctx.moveTo(px-sx/2,py);ctx.lineTo(px+sx/2,py);ctx.lineTo(px+sx/1.7,10*perspective);ctx.lineTo(px-sx/1.7,10*perspective);ctx.closePath();ctx.fillStyle=grad;ctx.fill();
    }
    ctx.restore();

    requestAnimationFrame(render);
  }
  requestAnimationFrame(render);
})();
</script>
</body></html>
