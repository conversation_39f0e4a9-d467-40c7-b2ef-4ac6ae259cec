<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <title>3D Spacetime Grid (Fixed)</title>
  <meta name="viewport" content="width=device-width,initial-scale=1" />
  <style>
    html,body { height:100%; margin:0; background:#000; overflow:hidden; }
    #ui {
      position:absolute; left:12px; top:12px; color:#ddd;
      font-family:sans-serif; background:rgba(0,0,0,0.4);
      padding:10px; border-radius:8px; backdrop-filter:blur(6px);
      z-index:10;
    }
    label{display:block;font-size:13px;margin-top:6px;}
    input[type=range]{width:180px;}
  </style>
</head>
<body>
<div id="ui">
  <strong>3D Spacetime Grid</strong><br>
  <label>Depth: <span id="depthVal">60</span></label>
  <input id="depth" type="range" min="20" max="160" value="60">
  <label>Spacing: <span id="spacingVal">6</span></label>
  <input id="spacing" type="range" min="2" max="20" value="6">
  <label>Flow speed: <span id="speedVal">0.8</span></label>
  <input id="speed" type="range" min="0" max="3" step="0.05" value="0.8">
  <label>Grid intensity: <span id="intVal">1.0</span></label>
  <input id="intensity" type="range" min="0.1" max="2" step="0.05" value="1">
</div>

<script type="module">
import * as THREE from 'https://unpkg.com/three@0.154.0/build/three.module.js';
import { OrbitControls } from 'https://unpkg.com/three@0.154.0/examples/jsm/controls/OrbitControls.js';

let PARAMS = { depth:60, spacing:6, speed:0.8, intensity:1 };
const scene = new THREE.Scene();
scene.fog = new THREE.FogExp2(0x000010, 0.0018);

const camera = new THREE.PerspectiveCamera(55, innerWidth/innerHeight, 0.1, 2000);
camera.position.set(100, 100, 160);
camera.lookAt(0,0,0);

const renderer = new THREE.WebGLRenderer({antialias:true});
renderer.setSize(innerWidth,innerHeight);
renderer.setPixelRatio(Math.min(window.devicePixelRatio,2));
document.body.appendChild(renderer.domElement);

const controls = new OrbitControls(camera, renderer.domElement);
controls.enableDamping = true;

// Lighting
scene.add(new THREE.AmbientLight(0xffffff, 0.15));
const dir = new THREE.DirectionalLight(0x88ccff, 0.5);
dir.position.set(50,120,80);
scene.add(dir);

const gridGroup = new THREE.Group();
scene.add(gridGroup);

function buildGrid(){
  while(gridGroup.children.length) gridGroup.remove(gridGroup.children[0]);
  const depth = PARAMS.depth, spacing = PARAMS.spacing, halfSpan = 60;
  const planes = Math.floor((depth*2)/spacing)+1;

  const pos = [], col=[];
  const colorNear = new THREE.Color(0x00ffff);
  const colorFar  = new THREE.Color(0x003344);

  for(let p=0;p<planes;p++){
    const z = -depth + p*spacing;
    const t = p/(planes-1);
    const c = colorFar.clone().lerp(colorNear,1 - Math.abs(0.5 - t)*2);
    for(let x=-halfSpan;x<=halfSpan;x+=spacing){
      pos.push(x,-halfSpan,z,x,halfSpan,z);
      col.push(c.r,c.g,c.b,c.r,c.g,c.b);
    }
    for(let y=-halfSpan;y<=halfSpan;y+=spacing){
      pos.push(-halfSpan,y,z,halfSpan,y,z);
      col.push(c.r,c.g,c.b,c.r,c.g,c.b);
    }
  }

  const g = new THREE.BufferGeometry();
  g.setAttribute('position',new THREE.Float32BufferAttribute(pos,3));
  g.setAttribute('color',new THREE.Float32BufferAttribute(col,3));
  const m = new THREE.LineBasicMaterial({vertexColors:true,transparent:true});
  const lines = new THREE.LineSegments(g,m);
  gridGroup.add(lines);
}

let flowPoints;
function buildFlow(){
  if(flowPoints) scene.remove(flowPoints);
  const count=1000, depth=PARAMS.depth;
  const positions=new Float32Array(count*3);
  const speeds=new Float32Array(count);
  for(let i=0;i<count;i++){
    positions[i*3]=(Math.random()-0.5)*120;
    positions[i*3+1]=(Math.random()-0.5)*120;
    positions[i*3+2]=-depth + Math.random()*depth*2;
    speeds[i]=0.4+Math.random()*1.6;
  }
  const geom=new THREE.BufferGeometry();
  geom.setAttribute('position',new THREE.BufferAttribute(positions,3));
  geom.setAttribute('speed',new THREE.BufferAttribute(speeds,1));
  const mat=new THREE.PointsMaterial({color:0xffffff,size:1,opacity:0.9,transparent:true});
  flowPoints=new THREE.Points(geom,mat);
  scene.add(flowPoints);
}

function buildRing(){
  const seg=256, pts=[];
  for(let i=0;i<seg;i++){
    const a=i/seg*Math.PI*2;
    pts.push(new THREE.Vector3(Math.cos(a)*18,Math.sin(a)*18,0));
  }
  const g=new THREE.BufferGeometry().setFromPoints(pts);
  const ring=new THREE.LineLoop(g,new THREE.LineBasicMaterial({color:0x88ffff,transparent:true,opacity:0.6}));
  ring.rotation.x=Math.PI*0.5;
  scene.add(ring);
}

buildGrid();
buildFlow();
buildRing();

const clock = new THREE.Clock();
function animate(){
  requestAnimationFrame(animate);
  const t=clock.getElapsedTime(), dt=clock.getDelta();
  controls.update();

  gridGroup.rotation.y = Math.sin(t*0.1)*0.05;
  const g = gridGroup.children[0];
  if(g){
    const col=g.geometry.attributes.color;
    for(let i=0;i<col.count;i++){
      const r=col.getX(i), g1=col.getY(i), b=col.getZ(i);
      const wave=0.4+0.6*Math.sin(t*0.8 + i*0.005);
      col.setXYZ(i,r*wave*PARAMS.intensity,g1*wave*PARAMS.intensity,b*wave*PARAMS.intensity);
    }
    col.needsUpdate=true;
  }

  if(flowPoints){
    const pos=flowPoints.geometry.attributes.position;
    const spd=flowPoints.geometry.attributes.speed;
    for(let i=0;i<pos.count;i++){
      let z=pos.getZ(i)-dt*20*PARAMS.speed*spd.getX(i);
      if(z<-PARAMS.depth) z+=PARAMS.depth*2;
      pos.setZ(i,z);
    }
    pos.needsUpdate=true;
  }

  renderer.render(scene,camera);
}
animate();

window.addEventListener('resize',()=>{
  camera.aspect=innerWidth/innerHeight;
  camera.updateProjectionMatrix();
  renderer.setSize(innerWidth,innerHeight);
});

// UI
const depthEl=document.getElementById('depth');
const spacingEl=document.getElementById('spacing');
const speedEl=document.getElementById('speed');
const intEl=document.getElementById('intensity');

function rebuild(){
  buildGrid();
  buildFlow();
}
depthEl.oninput=e=>{PARAMS.depth=+e.target.value;document.getElementById('depthVal').innerText=PARAMS.depth;rebuild();}
spacingEl.oninput=e=>{PARAMS.spacing=+e.target.value;document.getElementById('spacingVal').innerText=PARAMS.spacing;rebuild();}
speedEl.oninput=e=>{PARAMS.speed=+e.target.value;document.getElementById('speedVal').innerText=PARAMS.speed.toFixed(2);}
intEl.oninput=e=>{PARAMS.intensity=+e.target.value;document.getElementById('intVal').innerText=PARAMS.intensity.toFixed(2);}
</script>
</body>
</html>
