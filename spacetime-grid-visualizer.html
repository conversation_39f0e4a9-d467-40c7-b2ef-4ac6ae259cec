<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8" />
<meta name="viewport" content="width=device-width,initial-scale=1" />
<title>Spacetime Grid Music Visualizer</title>
<style>
  html,body { margin:0; height:100%; background:black; overflow:hidden; }
  canvas { position:fixed; top:0; left:0; width:100%; height:100%; display:block; }
  .ui {
    position: fixed;
    top: 14px;
    left: 14px;
    display: flex;
    gap: 8px;
    z-index: 10;
    font-family: sans-serif;
  }
  .btn {
    background: rgba(0,0,0,0.5);
    color: #bfffbf;
    border: 1px solid rgba(0,255,170,0.2);
    border-radius: 8px;
    padding: 6px 12px;
    cursor: pointer;
    font-weight: bold;
  }
  .btn:active { transform: translateY(1px); }
  #fileInput { display: none; }
</style>
</head>
<body>
<canvas id="gl"></canvas>
<canvas id="overlay"></canvas>

<div class="ui">
  <button class="btn" id="loadBtn">🎵 LOAD AUDIO</button>
  <button class="btn" id="playBtn">⏯ PLAY</button>
  <input type="file" id="fileInput" accept="audio/*">
</div>

<script>
const glCanvas = document.getElementById('gl');
const overlay = document.getElementById('overlay');
const gl = glCanvas.getContext('webgl');

function resize() {
  const dpr = window.devicePixelRatio || 1;
  glCanvas.width = innerWidth * dpr;
  glCanvas.height = innerHeight * dpr;
  overlay.width = innerWidth * dpr;
  overlay.height = innerHeight * dpr;
  gl.viewport(0,0,glCanvas.width, glCanvas.height);
}
window.addEventListener('resize', resize);

const vsSource = `attribute vec2 position;
void main() {
  gl_Position = vec4(position,0.0,1.0);
}`;

const fsSource = `
precision mediump float;
uniform vec2 resolution;
uniform float time;
uniform float bass;
float grid(vec3 p) {
  vec3 q = fract(p*6.0) - 0.5;
  vec3 g = abs(q);
  float line = min(min(g.x,g.y),g.z);
  return 1.0 - smoothstep(0.0,0.02,line);
}
vec3 warp(vec3 p, float t, float c) {
  float r = length(p.xy);
  float pull = smoothstep(0.0,1.2,1.2-r)*c*0.8;
  p.xy *= 1.0 - pull*0.5;
  p.z += t*0.6;
  return p;
}
void main() {
  vec2 uv = (gl_FragCoord.xy/resolution.xy)*2.0-1.0;
  uv.x *= resolution.x/resolution.y;
  float acc=0.0;
  for(float i=0.0;i<50.0;i++){
    float d=i*0.1;
    vec3 pos=warp(vec3(uv*2.0,-d*2.0),time,bass);
    acc+=grid(pos)/(1.0+d*d);
  }
  acc=clamp(acc*1.5,0.0,1.0);
  vec3 col = mix(vec3(0.02,0.06,0.04),vec3(0.1,0.9,0.8),acc);
  gl_FragColor = vec4(col,1.0);
}`;

function compileShader(src,type){
  const s=gl.createShader(type);
  gl.shaderSource(s,src);
  gl.compileShader(s);
  return s;
}
const vs=compileShader(vsSource,gl.VERTEX_SHADER);
const fs=compileShader(fsSource,gl.FRAGMENT_SHADER);
const prog=gl.createProgram();
gl.attachShader(prog,vs);gl.attachShader(prog,fs);
gl.linkProgram(prog);gl.useProgram(prog);

const quad=gl.createBuffer();
gl.bindBuffer(gl.ARRAY_BUFFER,quad);
gl.bufferData(gl.ARRAY_BUFFER,new Float32Array([-1,-1,1,-1,-1,1,1,1]),gl.STATIC_DRAW);
const posLoc=gl.getAttribLocation(prog,"position");
gl.enableVertexAttribArray(posLoc);
gl.vertexAttribPointer(posLoc,2,gl.FLOAT,false,0,0);

const uRes=gl.getUniformLocation(prog,"resolution");
const uTime=gl.getUniformLocation(prog,"time");
const uBass=gl.getUniformLocation(prog,"bass");

let audioCtx, analyser, dataArray, audio;
function setupAudio(){
  audioCtx=new (window.AudioContext||window.webkitAudioContext)();
  analyser=audioCtx.createAnalyser();
  analyser.fftSize=1024;
  dataArray=new Uint8Array(analyser.frequencyBinCount);
}

function getBass(){
  if(!analyser)return 0;
  analyser.getByteFrequencyData(dataArray);
  let sum=0,count=dataArray.length*0.12;
  for(let i=0;i<count;i++)sum+=dataArray[i];
  return (sum/count)/255;
}

const ctx=overlay.getContext('2d');
function drawOverlay(bass){
  ctx.clearRect(0,0,overlay.width,overlay.height);
  const dpr=window.devicePixelRatio||1;
  ctx.save();
  ctx.scale(dpr,dpr);
  const w=overlay.width/dpr,h=overlay.height/dpr;
  const cx=w/2,cy=h/2;
  const radius=Math.min(w,h)*0.15;
  ctx.strokeStyle=`rgba(180,255,240,0.3)`;
  ctx.lineWidth=2;
  ctx.beginPath();
  ctx.arc(cx,cy,radius,0,Math.PI*2);
  ctx.stroke();
  // draw ball
  const r=radius*0.4 + bass*80;
  const g=ctx.createRadialGradient(cx,cy,0,cx,cy,r);
  g.addColorStop(0,'rgba(200,255,240,0.9)');
  g.addColorStop(1,'rgba(20,40,30,0.0)');
  ctx.fillStyle=g;
  ctx.beginPath();
  ctx.arc(cx,cy,r,0,Math.PI*2);
  ctx.fill();
  ctx.restore();
}

function render(t){
  gl.uniform2f(uRes,glCanvas.width,glCanvas.height);
  gl.uniform1f(uTime,t*0.001);
  const bass=getBass();
  gl.uniform1f(uBass,bass);
  gl.drawArrays(gl.TRIANGLE_STRIP,0,4);
  drawOverlay(bass);
  requestAnimationFrame(render);
}

resize();
requestAnimationFrame(render);

// UI
document.getElementById('loadBtn').addEventListener('click',()=>document.getElementById('fileInput').click());
document.getElementById('fileInput').addEventListener('change',e=>{
  const file=e.target.files[0]; if(!file)return;
  if(!audioCtx)setupAudio();
  if(audio)audio.pause();
  audio=new Audio(URL.createObjectURL(file));
  const src=audioCtx.createMediaElementSource(audio);
  src.connect(analyser); analyser.connect(audioCtx.destination);
  audio.play();
});
document.getElementById('playBtn').addEventListener('click',()=>{
  if(!audio)return;
  if(audio.paused)audio.play(); else audio.pause();
});
</script>
</body>
</html>
