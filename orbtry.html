<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8" />
<meta name="viewport" content="width=device-width,initial-scale=1" />
<title>Energy Orb — Music Visualizer</title>
<style>
  :root{
    --bg:#07070b;
    --panel: rgba(255,255,255,0.04);
    --accent: #66e0ff;
  }
  html,body{height:100%;margin:0;background:var(--bg);color:#e6eef6;font-family:Inter,Roboto,system-ui,Arial;}
  #ui{
    position:fixed;left:12px;top:12px;z-index:50;
    width:320px;padding:12px;border-radius:12px;background:linear-gradient(180deg, rgba(255,255,255,0.03), rgba(255,255,255,0.02));
    box-shadow:0 6px 20px rgba(0,0,0,0.6);backdrop-filter:blur(6px);
  }
  h1{margin:0 0 8px 0;font-size:16px}
  label{display:block;font-size:12px;margin:8px 0 4px 0;opacity:0.9}
  input[type=range]{width:100%}
  button{margin-right:8px;padding:8px 10px;border-radius:8px;border:0;background:var(--accent);color:#022a2f;font-weight:700;cursor:pointer}
  button.secondary{background:transparent;color:#cfeff6;border:1px solid rgba(255,255,255,0.06)}
  small{opacity:0.7}
  #canvas{display:block;width:100vw;height:100vh}
  #footer{position:fixed;right:12px;bottom:12px;color:#9fbfc9;font-size:12px;opacity:0.9}
  .row{display:flex;gap:8px;align-items:center;}
  .muted{opacity:0.6}
</style>
</head>
<body>
<div id="ui">
  <h1>Energy Orb — Music Visualizer</h1>
  <div class="row">
    <button id="micBtn">Use Microphone</button>
    <button id="fileBtn" class="secondary">Load Audio File</button>
    <input id="fileInput" type="file" accept="audio/*" style="display:none" />
  </div>

  <label>Sensitivity <small class="muted">(amplitude multiplier)</small></label>
  <input id="sensitivity" type="range" min="0.2" max="6" step="0.1" value="1.6">

  <label>Smoothing <small class="muted">(0 = jumpy, 0.98 = very smooth)</small></label>
  <input id="smoothing" type="range" min="0" max="0.98" step="0.01" value="0.82">

  <label>Node Density <small class="muted">(more nodes = heavier CPU)</small></label>
  <input id="density" type="range" min="300" max="2500" step="50" value="980">

  <label>Orb Size <small class="muted">(visual scale)</small></label>
  <input id="scale" type="range" min="0.8" max="1.8" step="0.05" value="1.0">

  <label>Color Shift</label>
  <input id="hue" type="range" min="0" max="360" step="1" value="190">

  <div style="margin-top:8px">
    <small class="muted">Drag to rotate • Scroll to zoom • Click "Use Microphone" or load a file</small>
  </div>
</div>

<canvas id="canvas"></canvas>
<div id="footer">Energy Orb • Self-contained HTML visualizer</div>

<script>
// Energy Orb Visualizer
// Self-contained single-file visualizer with Web Audio Analyser + Canvas 2D rendering.
// - Nodes placed on a sphere surface (approx. uniform distribution using Fibonacci sphere).
// - Each node samples a frequency band mapped by its longitude to produce ripples.
// - Transparent/soft rendering to create an ethereal orb look.
// - Mouse drag rotates orb. Scroll zooms. Controls for sens, smoothing, density, scale, hue.

(() => {
  const canvas = document.getElementById('canvas');
  const ctx = canvas.getContext('2d', { alpha: true });

  let W = canvas.width = innerWidth;
  let H = canvas.height = innerHeight;
  let DPR = Math.max(1, devicePixelRatio || 1);
  canvas.width = Math.floor(W * DPR);
  canvas.height = Math.floor(H * DPR);
  canvas.style.width = W + 'px';
  canvas.style.height = H + 'px';
  ctx.scale(DPR, DPR);

  // UI refs
  const micBtn = document.getElementById('micBtn');
  const fileBtn = document.getElementById('fileBtn');
  const fileInput = document.getElementById('fileInput');
  const sensitivityRange = document.getElementById('sensitivity');
  const smoothingRange = document.getElementById('smoothing');
  const densityRange = document.getElementById('density');
  const scaleRange = document.getElementById('scale');
  const hueRange = document.getElementById('hue');

  // Audio context + analyser
  let audioCtx = null;
  let analyser = null;
  let sourceNode = null;
  let freqData = null;
  const FFT_SIZE = 2048;

  // Orb geometry & state
  let nodes = [];
  let NODE_COUNT = parseInt(densityRange.value, 10);
  let orbRadius = Math.min(W, H) * 0.22 * parseFloat(scaleRange.value);
  let rotationX = 0.2;
  let rotationY = 0.8;
  let targetRotX = rotationX;
  let targetRotY = rotationY;
  let zoom = 1.0;
  let hue = parseInt(hueRange.value, 10);

  // smoothing
  let smoothing = parseFloat(smoothingRange.value);

  // Build fibonacci sphere distribution
  function buildNodes(count) {
    nodes = new Array(count);
    const golden = Math.PI * (3 - Math.sqrt(5));
    for (let i = 0; i < count; i++) {
      const t = i / (count - 1);
      const y = 1 - 2 * t;
      const r = Math.sqrt(1 - y * y);
      const phi = i * golden;
      const x = Math.cos(phi) * r;
      const z = Math.sin(phi) * r;
      // longitude angle (0..1) - map to audio band
      const lon = (Math.atan2(z, x) + Math.PI) / (2 * Math.PI);
      nodes[i] = {
        x, y, z, lon,
        // dynamic state
        disp: 0,
        decay: 0,
        alpha: 0
      };
    }
  }

  // Initialize nodes
  buildNodes(NODE_COUNT);

  // Audio helpers
  async function initAudio() {
    if (!audioCtx) {
      audioCtx = new (window.AudioContext || window.webkitAudioContext)();
    }
    analyser = audioCtx.createAnalyser();
    analyser.fftSize = FFT_SIZE;
    analyser.smoothingTimeConstant = smoothing;
    const bufferLength = analyser.frequencyBinCount;
    freqData = new Uint8Array(bufferLength);
  }

  async function useMicrophone() {
    try {
      await initAudio();
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      if (sourceNode) {
        try { sourceNode.disconnect(); } catch (e) {}
      }
      sourceNode = audioCtx.createMediaStreamSource(stream);
      sourceNode.connect(analyser);
      micBtn.textContent = 'Mic: On';
      micBtn.style.background = '#ffb86b';
      fileBtn.classList.remove('active');
    } catch (err) {
      alert('Microphone access was denied or not available.\nYou can load an audio file instead.');
      console.error(err);
    }
  }

  function useFileAudio(file) {
    // create source from <audio>
    if (!file) return;
    initAudio().then(() => {
      if (sourceNode) {
        try { sourceNode.disconnect(); } catch (e) {}
      }
      const url = URL.createObjectURL(file);
      const audio = new Audio(url);
      audio.crossOrigin = "anonymous";
      audio.loop = false;
      audio.controls = false;
      audio.autoplay = true;
      audio.style.display = "none";
      document.body.appendChild(audio);
      sourceNode = audioCtx.createMediaElementSource(audio);
      sourceNode.connect(analyser);
      analyser.connect(audioCtx.destination);
      micBtn.textContent = 'Use Microphone';
      micBtn.style.background = '';
      fileBtn.classList.add('active');
      audio.play().catch(e => {
        // resume audio context on user gesture
        audioCtx.resume().then(()=>audio.play());
      });
    }).catch(console.error);
  }

  // Responsive
  window.addEventListener('resize', () => {
    W = canvas.width = innerWidth;
    H = canvas.height = innerHeight;
    canvas.width = Math.floor(W * DPR);
    canvas.height = Math.floor(H * DPR);
    canvas.style.width = W + 'px';
    canvas.style.height = H + 'px';
    ctx.setTransform(DPR, 0, 0, DPR, 0, 0);
    orbRadius = Math.min(W, H) * 0.22 * parseFloat(scaleRange.value);
  });

  // Mouse interaction for rotation/zoom
  let dragging = false;
  let lastX = 0, lastY = 0;
  canvas.addEventListener('pointerdown', (e) => {
    dragging = true;
    lastX = e.clientX;
    lastY = e.clientY;
    canvas.setPointerCapture(e.pointerId);
  });
  canvas.addEventListener('pointermove', (e) => {
    if (!dragging) return;
    const dx = (e.clientX - lastX) / Math.max(1, Math.min(W, H));
    const dy = (e.clientY - lastY) / Math.max(1, Math.min(W, H));
    targetRotY += dx * 6.0;
    targetRotX += dy * 6.0;
    lastX = e.clientX; lastY = e.clientY;
  });
  window.addEventListener('pointerup', (e) => {
    dragging = false;
  });
  canvas.addEventListener('wheel', (e) => {
    e.preventDefault();
    zoom *= (1 - e.deltaY * 0.0006);
    zoom = Math.max(0.5, Math.min(2.4, zoom));
  }, { passive:false });

  // Map node lon -> frequency index (smooth)
  function getBandForLon(lon) {
    // lon in [0,1]; map to log-ish frequency bins to emphasize low/mid
    if (!freqData) return 0;
    const n = freqData.length;
    // use a nonlinear mapping (power curve)
    const idx = Math.floor(Math.pow(lon, 0.75) * (n - 1));
    return Math.max(0, Math.min(n - 1, idx));
  }

  // color helpers
  function hslToCss(h, s, l, a=1) {
    return `hsla(${h}, ${s}%, ${l}%, ${a})`;
  }

  // Render loop
  function render() {
    requestAnimationFrame(render);

    // smooth rotation
    rotationX += (targetRotX - rotationX) * 0.08;
    rotationY += (targetRotY - rotationY) * 0.08;

    // get audio data
    if (analyser && freqData) {
      analyser.getByteFrequencyData(freqData);
      // apply smoothing control
      analyser.smoothingTimeConstant = smoothing;
    }

    // clear
    ctx.clearRect(0,0,W,H);

    // subtle background radial glow
    const g = ctx.createRadialGradient(W/2, H/2, Math.min(W,H)*0.02, W/2, H/2, Math.max(W,H)*0.9);
    g.addColorStop(0, `rgba(8,12,18,0.35)`);
    g.addColorStop(1, `rgba(4,6,10,0.0)`);
    ctx.fillStyle = g;
    ctx.fillRect(0,0,W,H);

    // center
    const cx = W / 2;
    const cy = H / 2;

    // precompute rotation matrices (X then Y)
    const sinX = Math.sin(rotationX), cosX = Math.cos(rotationX);
    const sinY = Math.sin(rotationY), cosY = Math.cos(rotationY);

    // draw nodes. We'll render in painter's order based on depth (z after rotation)
    // compute projected nodes
    const projected = [];
    const sens = parseFloat(sensitivityRange.value);
    const sc = parseFloat(scaleRange.value) * zoom;
    const baseR = orbRadius * sc;

    for (let i = 0; i < nodes.length; i++) {
      const n = nodes[i];

      // determine audio amplitude for the node's longitude
      if (freqData) {
        const idx = getBandForLon(n.lon);
        // average small neighborhood for smoother per-node values:
        let v = 0;
        const w = 3;
        for (let k = -w; k <= w; k++) {
          const j = Math.max(0, Math.min(freqData.length - 1, idx + k));
          v += freqData[j];
        }
        v = v / (2*w+1); // 0..255
        // scale to displacement
        const disp = (v / 255) * baseR * 0.65 * sens;
        // smoothing per node
        n.disp += (disp - n.disp) * 0.35;
        n.alpha = Math.min(1, (v/255) * 1.8);
      } else {
        // idle subtle breathing
        const t = performance.now() * 0.001 * 0.4;
        n.disp = (Math.sin(t + n.lon*12 + n.y*6) + 1) * 0.02 * baseR;
        n.alpha = 0.18;
      }

      // get position with displacement along radial normal
      const rx = n.x * (1 + n.disp / baseR);
      const ry = n.y * (1 + n.disp / baseR);
      const rz = n.z * (1 + n.disp / baseR);

      // rotate X
      let ry2 = ry * cosX - rz * sinX;
      let rz2 = ry * sinX + rz * cosX;
      // rotate Y
      let rx3 = rx * cosY + rz2 * sinY;
      let rz3 = -rx * sinY + rz2 * cosY;

      // perspective projection
      const perspective = 1.0 / (1.8 - rz3); // tune depth
      const px = cx + rx3 * baseR * perspective;
      const py = cy + ry2 * baseR * perspective;
      const scale = Math.max(0.1, perspective * 1.45);

      projected.push({
        i, px, py, scale, depth: rz3, alpha: n.alpha, lon: n.lon, disp: n.disp
      });
    }

    // sort back-to-front
    projected.sort((a,b) => a.depth - b.depth);

    // draw subtle connections (nearest neighbors) to hint surface structure
    // For performance, sample a fraction of nodes to connect.
    const connCount = Math.min(600, projected.length);
    ctx.lineWidth = 0.6;
    for (let i = 0; i < connCount; i += 2) {
      const a = projected[i];
      // connect to a few nearest in screen-space among subsequent nodes
      let connections = 0;
      for (let j = i + 1; j < Math.min(projected.length, i + 60) && connections < 2; j++) {
        const b = projected[j];
        const dx = b.px - a.px;
        const dy = b.py - a.py;
        const d2 = dx*dx + dy*dy;
        if (d2 < (140*140) * (a.scale + b.scale)) {
          const g2 = ctx.createLinearGradient(a.px, a.py, b.px, b.py);
          const alpha = Math.min(0.55, (a.alpha + b.alpha) * 0.35);
          g2.addColorStop(0, hslToCss(hue + a.lon*80, 80, 60, alpha));
          g2.addColorStop(1, hslToCss(hue + b.lon*80, 80, 60, alpha*0.4));
          ctx.strokeStyle = g2;
          ctx.beginPath();
          ctx.moveTo(a.px, a.py);
          ctx.lineTo(b.px, b.py);
          ctx.stroke();
          connections++;
        }
      }
    }

    // draw nodes (glow + core)
    for (let k = 0; k < projected.length; k++) {
      const p = projected[k];
      const s = p.scale * 2.6; // size factor
      const alpha = Math.min(1, Math.max(0.04, p.alpha));
      // outer glow
      const rg = ctx.createRadialGradient(p.px, p.py, 0, p.px, p.py, 22 * s);
      rg.addColorStop(0, hslToCss(hue + p.lon*80, 90, 60, alpha * 0.55));
      rg.addColorStop(0.2, hslToCss(hue + p.lon*60, 80, 50, alpha * 0.25));
      rg.addColorStop(1, 'rgba(0,0,0,0)');
      ctx.fillStyle = rg;
      ctx.beginPath();
      ctx.arc(p.px, p.py, 22 * s, 0, Math.PI*2);
      ctx.fill();

      // core
      ctx.beginPath();
      ctx.fillStyle = hslToCss(hue + p.lon*90, 95, 60, Math.min(1, alpha*1.1));
      ctx.arc(p.px, p.py, 2.6 * s, 0, Math.PI*2);
      ctx.fill();

      // add faint ring around some front nodes for depth
      if (p.depth > 0.1 && Math.random() > 0.997) {
        ctx.strokeStyle = hslToCss(hue + p.lon*80, 80, 60, 0.08);
        ctx.lineWidth = 1.0 * s;
        ctx.beginPath();
        ctx.arc(p.px, p.py, 14 * s, 0, Math.PI*2);
        ctx.stroke();
      }
    }

    // final overlay: faint glass reflection
    const overlay = ctx.createLinearGradient(0,0,0,H);
    overlay.addColorStop(0, 'rgba(255,255,255,0.02)');
    overlay.addColorStop(0.6, 'rgba(255,255,255,0.01)');
    ctx.fillStyle = overlay;
    ctx.fillRect(0,0,W,H);

    // small HUD: show FPS-like indicator
    // (kept minimal to not distract)
  }

  // Start render loop even before audio
  render();

  // UI event wiring
  micBtn.addEventListener('click', async () => {
    // user gesture may be required to resume audio
    if (!audioCtx) {
      await initAudio();
    }
    try {
      await audioCtx.resume();
    } catch (e) {}
    useMicrophone();
  });

  fileBtn.addEventListener('click', () => fileInput.click());
  fileInput.addEventListener('change', (e) => {
    const f = e.target.files && e.target.files[0];
    if (f) useFileAudio(f);
  });

  sensitivityRange.addEventListener('input', (e) => {});
  smoothingRange.addEventListener('input', (e) => {
    smoothing = parseFloat(smoothingRange.value);
    if (analyser) analyser.smoothingTimeConstant = smoothing;
  });
  densityRange.addEventListener('input', (e) => {
    const val = parseInt(densityRange.value,10);
    if (val !== nodes.length) {
      NODE_COUNT = val;
      buildNodes(NODE_COUNT);
    }
  });
  scaleRange.addEventListener('input', (e) => {
    orbRadius = Math.min(W, H) * 0.22 * parseFloat(scaleRange.value);
  });
  hueRange.addEventListener('input', (e) => {
    hue = parseInt(hueRange.value, 10);
  });

  // helpful fallback tone when no audio (optional)
  // We won't auto-create sound; user triggers mic or file.

  // warm initial animation: subtle rotation changes
  setInterval(() => {
    if (!dragging) {
      targetRotY += 0.002;
      targetRotX = 0.12 + Math.sin(performance.now()*0.0005) * 0.05;
    }
  }, 30);

  // Accessibility note: resume audio context on first user gesture (some browsers)
  function resumeAudioCtxOnGesture() {
    if (!audioCtx) return;
    if (audioCtx.state === 'suspended') {
      audioCtx.resume();
    }
    window.removeEventListener('click', resumeAudioCtxOnGesture);
    window.removeEventListener('keydown', resumeAudioCtxOnGesture);
  }
  window.addEventListener('click', resumeAudioCtxOnGesture);
  window.addEventListener('keydown', resumeAudioCtxOnGesture);

  // initial setup done
})();
</script>
</body>
</html>
