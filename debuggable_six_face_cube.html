<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8" />
<title>Debuggable Six-Face Cube</title>
<meta name="viewport" content="width=device-width,initial-scale=1" />
<style>
  html,body { margin:0; height:100%; background:#000; }
  canvas { display:block; background:#000; }
</style>
</head>
<body>
<canvas id="c"></canvas>
<script>
const canvas = document.getElementById("c");
const ctx = canvas.getContext("2d");
resize();
window.addEventListener("resize", resize);

let angle = 0;
let panels = [];

function resize(){
  canvas.width = innerWidth;
  canvas.height = innerHeight;
  buildPanels();
}

function buildPanels(){
  panels = [];
  const s = 120;
  panels.push({name:"front",x:0,y:0,z:s,color:"red"});
  panels.push({name:"back",x:0,y:0,z:-s,color:"blue"});
  panels.push({name:"top",x:0,y:s,z:0,color:"green"});
  panels.push({name:"bottom",x:0,y:-s,z:0,color:"yellow"});
  panels.push({name:"right",x:s,y:0,z:0,color:"magenta"});
  panels.push({name:"left",x:-s,y:0,z:0,color:"cyan"});
}

function rotateX(p,a){
  const c=Math.cos(a),s=Math.sin(a);
  return {x:p.x,y:p.y*c-p.z*s,z:p.y*s+p.z*c};
}
function rotateY(p,a){
  const c=Math.cos(a),s=Math.sin(a);
  return {x:p.x*c+p.z*s,y:p.y,z:-p.x*s+p.z*c};
}
function project(p){
  const f=400;
  const z = f + p.z;
  if (z <= 1) return null;
  const scale = f / z;
  return {x:p.x*scale+canvas.width/2, y:p.y*scale+canvas.height/2, scale};
}

function drawWireframeCube(){
  try {
    const size=120;
    const points=[
      {x:-size,y:-size,z:-size},
      {x:size,y:-size,z:-size},
      {x:size,y:size,z:-size},
      {x:-size,y:size,z:-size},
      {x:-size,y:-size,z:size},
      {x:size,y:-size,z:size},
      {x:size,y:size,z:size},
      {x:-size,y:size,z:size},
    ].map(p=>{
      p=rotateY(p,angle);
      p=rotateX(p,angle*0.6);
      return project(p);
    });

    ctx.strokeStyle="rgba(0,255,0,0.3)";
    ctx.beginPath();
    const edges=[
      [0,1],[1,2],[2,3],[3,0],
      [4,5],[5,6],[6,7],[7,4],
      [0,4],[1,5],[2,6],[3,7]
    ];
    for(let e of edges){
      const a = points[e[0]], b = points[e[1]];
      if (!a || !b) continue;
      ctx.moveTo(a.x,a.y);
      ctx.lineTo(b.x,b.y);
    }
    ctx.stroke();
  } catch(err){
    console.error("Wireframe draw error:", err);
  }
}

function draw(){
  ctx.clearRect(0,0,canvas.width,canvas.height);
  angle += 0.01;

  for (let p of panels) {
    try {
      let pos = rotateY(p,angle);
      pos = rotateX(pos,angle*0.6);
      const proj = project(pos);
      if (!proj) {
        console.warn("Skipped panel:", p.name, "z too close:", pos.z);
        continue;
      }
      const size = 80 * proj.scale;
      ctx.fillStyle = p.color;
      ctx.globalAlpha = 0.8;
      ctx.fillRect(proj.x - size/2, proj.y - size/2, size, size);
    } catch(err) {
      console.error("Panel draw error:", p.name, err);
    }
  }

  drawWireframeCube();
  requestAnimationFrame(draw);
}
draw();
</script>
</body>
</html>
