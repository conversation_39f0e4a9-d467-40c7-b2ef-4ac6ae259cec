<!DOCTYPE html>
<html>
<head>
    <title>Spacetime Grid + Dual-Sphere Visualizer (Fractal Removed)</title>
    <meta charset="utf-8"/>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&display=swap');
        html,body{margin:0;height:100%;background:radial-gradient(ellipse at center,#001122 0%,#000511 70%,#000000 100%);overflow:hidden;font-family:'Orbitron',monospace}
        canvas{width:100vw;height:100vh;display:block}
        .controls{position:fixed;top:15px;left:15px;color:#87CEEB;background:linear-gradient(135deg,rgba(0,5,17,0.95) 0%, rgba(0,20,40,0.9) 100%);padding:20px;border-radius:12px;border:2px solid rgba(135,206,235,0.3);box-shadow:0 8px 32px rgba(135,206,235,0.1);backdrop-filter:blur(10px);font-weight:400;transition:opacity .8s}
        .controls strong{font-weight:900;font-size:16px;text-shadow:0 0 10px rgba(135,206,235,.5)}
        .physics-controls{position:fixed;color:#87CEEB;background:linear-gradient(135deg,rgba(0,5,17,0.95) 0%, rgba(0,20,40,0.9) 100%);padding:20px;border-radius:12px;border:2px solid rgba(135,206,235,.3);box-shadow:0 8px 32px rgba(135,206,235,.1);backdrop-filter:blur(10px);transition:all .4s cubic-bezier(.4,0,.2,1),opacity .8s;min-width:280px;max-width:320px;max-height:calc(100vh-40px);overflow-y:auto}
        .toggle-button{position:absolute;top:-40px;right:15px;background:linear-gradient(135deg,rgba(0,5,17,0.95) 0%, rgba(0,30,60,0.9) 100%);border:2px solid rgba(135,206,235,.4);color:#87CEEB;padding:8px 16px;border-radius:8px;cursor:pointer;font-family:'Orbitron',monospace;font-size:12px;font-weight:700;transition:all .3s}
        .slider-container{margin:12px 0;display:flex;align-items:center;gap:15px}
        input[type="file"]{color:#87CEEB;background:rgba(135,206,235,.1);border:1px solid rgba(135,206,235,.3);border-radius:6px;padding:6px 10px;font-family:'Orbitron',monospace;font-size:11px}
        #canvasBG{position:fixed;left:0;top:0;z-index:1}
        #canvasFG{position:fixed;left:0;top:0;z-index:2}
        #audioControls{position:fixed;right:15px;bottom:15px;z-index:40}
        #pauseButton{background:linear-gradient(135deg,rgba(0,5,17,0.9) 0%, rgba(0,30,60,0.8) 100%);border:2px solid rgba(135,206,235,.4);color:#87CEEB;padding:8px 20px;border-radius:8px;cursor:pointer;font-family:'Orbitron',monospace;font-weight:700}
    </style>
</head>
<body>
  <div class="controls">
    <div style="display:flex;align-items:center;gap:10px;margin-bottom:10px"><span style="font-size:24px">🌌</span><strong>SPACETIME GRID (NO FRACTAL)</strong></div>
    <div style="font-size:11px;opacity:.9;line-height:1.4">
      <div><kbd>WASD</kbd> Move Camera</div>
      <div><kbd>Mouse</kbd> Look Around</div>
      <div><kbd>Q/E</kbd> Up/Down</div>
      <div><kbd>+/-</kbd> Quality</div>
    </div>
  </div>

  <div id="audioControls" class="physics-controls" style="right:15px;bottom:15px;">
    <button class="toggle-button" id="audioToggle">🎵 AUDIO</button>
    <div style="display:flex;align-items:center;gap:8px;margin-bottom:15px"><span style="font-size:16px">🎵</span><strong style="font-size:14px">AUDIO REACTIVE</strong></div>
    <div style="display:flex;flex-direction:column;align-items:flex-start;gap:8px">
      <label style="min-width:auto;color:#87CEEB;font-family:Orbitron,monospace;font-size:12px">🎵 Upload Music File</label>
      <input type="file" id="audioFile" accept="audio/*">
    </div>
    <div style="display:flex;justify-content:center;margin:15px 0;">
      <button id="pauseButton">⏸️ PAUSE</button>
    </div>
    <div style="margin-top:8px;color:#bfefff;font-size:11px" id="audioStatus">No audio loaded</div>
  </div>

  <canvas id="canvasBG"></canvas>
  <canvas id="canvasFG"></canvas>

<script>
// -------------------- sizing --------------------
const canvasBG = document.getElementById('canvasBG');
const canvasFG = document.getElementById('canvasFG');
function resizeAll(){
  const dpr = window.devicePixelRatio || 1;
  canvasBG.width = Math.floor(window.innerWidth * dpr);
  canvasBG.height = Math.floor(window.innerHeight * dpr);
  canvasBG.style.width = window.innerWidth + 'px';
  canvasBG.style.height = window.innerHeight + 'px';

  canvasFG.width = window.innerWidth;
  canvasFG.height = window.innerHeight;
  canvasFG.style.width = window.innerWidth + 'px';
  canvasFG.style.height = window.innerHeight + 'px';

  if (gl) gl.viewport(0,0,canvasBG.width, canvasBG.height);
}
window.addEventListener('resize', resizeAll);
resizeAll();

// -------------------- WebGL (copied spacetime grid & camera/orbit code) --------------------
const gl = canvasBG.getContext('webgl');
if(!gl){ alert('WebGL not available'); throw 0; }

// vertex (full-screen quad)
const vsrc = `
  attribute vec2 position;
  void main(){ gl_Position = vec4(position, 0.0, 1.0); }
`;

// Fragment shader: taken from your file but modified to *only* render spacetimeGrid sampling
// (Mandelbulb fractal code removed). spacetimeGrid() function and camera/orbit uniforms kept intact.
const fsrc = `
precision mediump float;
uniform vec2 resolution;
uniform vec3 cameraPos;
uniform vec2 cameraRot;
uniform float time;
uniform float gravityStrength;
uniform float gridDensity;
uniform float spacetimeWarp;
uniform float animSpeed;
uniform float bassLevel;
uniform float midLevel;
uniform float trebleLevel;
uniform float musicReactivity;
uniform float backLightIntensity;

// Hash for small noise
float hash(vec3 p){ p = fract(p * 0.3183099 + 0.1); p *= 17.0; return fract(p.x*p.y*p.z*(p.x+p.y+p.z)); }

// gravitational-like field strength (copied)
float gravitationalField(vec3 pos) {
    float dist = length(pos);
    return gravityStrength / (dist * dist + 0.1);
}

// spacetimeGrid function copied verbatim from your file (keeps warping and flow)
float spacetimeGrid(vec3 pos) {
    vec3 warpedPos = pos;
    float field = gravitationalField(pos);
    warpedPos += normalize(pos) * field * spacetimeWarp * 2.0;

    float midReaction = midLevel * musicReactivity;
    float flowSpeed = time * animSpeed * 3.0 * (1.0 + midReaction);
    float flowIntensity = 0.2 + midReaction * 0.3;

    warpedPos.x += sin(flowSpeed + pos.z * 0.5) * flowIntensity;
    warpedPos.y += cos(flowSpeed * 0.7 + pos.x * 0.3) * flowIntensity * 0.75;
    warpedPos.z += sin(flowSpeed * 0.5 + pos.y * 0.4) * flowIntensity * 0.5;

    vec3 animatedPos = warpedPos * gridDensity * 10.0;
    animatedPos += vec3(flowSpeed * 0.5, flowSpeed * 0.3, flowSpeed * 0.4);

    vec3 grid = abs(fract(animatedPos) - 0.5);
    float gridLine = min(min(grid.x, grid.y), grid.z);

    gridLine = smoothstep(0.0, 0.005, gridLine);

    return (1.0 - gridLine) * 2.5;
}

// camera helpers (copied)
vec3 getRayDir(vec2 uv, vec3 camPos, vec2 camRot) {
    vec3 forward = vec3(
        cos(camRot.y) * sin(camRot.x),
        sin(camRot.y),
        cos(camRot.y) * cos(camRot.x)
    );
    vec3 right = normalize(cross(forward, vec3(0.0, 1.0, 0.0)));
    vec3 up = normalize(cross(right, forward));
    return normalize(forward + uv.x * right + uv.y * up);
}

void main(){
    vec2 uv = (gl_FragCoord.xy * 2.0 - resolution) / min(resolution.x, resolution.y);
    vec3 rayDir = getRayDir(uv, cameraPos, cameraRot);

    // Accumulate volumetric grid intensity along the ray
    float gridIntensity = 0.0;
    float t = 1.5;
    for (int i = 0; i < 25; i++) {
        vec3 samplePos = cameraPos + rayDir * t;
        float g = spacetimeGrid(samplePos);
        float falloff = 1.0 / (t * t * 0.08 + 1.0);
        gridIntensity += g * falloff * 0.08;
        t += 0.4;
    }

    float pulse = 1.0 + sin(time * animSpeed * 4.0) * 0.3;
    vec3 gridColor = vec3(0.4, 0.8, 1.0) * gridIntensity * pulse;

    // center funnel glow — stronger with bass
    vec2 centerUV = (gl_FragCoord.xy - 0.5*resolution.xy) / resolution.y;
    float centerPull = 1.0 - smoothstep(0.0, 1.6, length(centerUV) * (1.0 + bassLevel*1.8));
    vec3 centerGlow = vec3(0.2,0.4,0.8) * centerPull * (0.8 + bassLevel*2.0) * gridIntensity;

    vec3 col = gridColor + centerGlow;

    // Vignette
    float vign = smoothstep(1.25, 0.35, length(centerUV));
    col *= vign;

    col = pow(col, vec3(0.9));
    gl_FragColor = vec4(col, 1.0);
}
`;

// Compile + link
function makeShader(type, src){
  const s = gl.createShader(type);
  gl.shaderSource(s, src);
  gl.compileShader(s);
  if(!gl.getShaderParameter(s, gl.COMPILE_STATUS)) console.error(gl.getShaderInfoLog(s));
  return s;
}
const prog = gl.createProgram();
gl.attachShader(prog, makeShader(gl.VERTEX_SHADER, vsrc));
gl.attachShader(prog, makeShader(gl.FRAGMENT_SHADER, fsrc));
gl.linkProgram(prog);
gl.useProgram(prog);

// quad
const verts = new Float32Array([-1,-1, 1,-1, -1,1, 1,1]);
const buf = gl.createBuffer();
gl.bindBuffer(gl.ARRAY_BUFFER, buf);
gl.bufferData(gl.ARRAY_BUFFER, verts, gl.STATIC_DRAW);
const posLoc = gl.getAttribLocation(prog, 'position');
gl.enableVertexAttribArray(posLoc);
gl.vertexAttribPointer(posLoc, 2, gl.FLOAT, false, 0, 0);

// Uniform locations (match names in shader)
const uniforms = {
  resolution: gl.getUniformLocation(prog, 'resolution'),
  cameraPos: gl.getUniformLocation(prog, 'cameraPos'),
  cameraRot: gl.getUniformLocation(prog, 'cameraRot'),
  time: gl.getUniformLocation(prog, 'time'),
  gravityStrength: gl.getUniformLocation(prog, 'gravityStrength'),
  gridDensity: gl.getUniformLocation(prog, 'gridDensity'),
  spacetimeWarp: gl.getUniformLocation(prog, 'spacetimeWarp'),
  animSpeed: gl.getUniformLocation(prog, 'animSpeed'),
  bassLevel: gl.getUniformLocation(prog, 'bassLevel'),
  midLevel: gl.getUniformLocation(prog, 'midLevel'),
  trebleLevel: gl.getUniformLocation(prog, 'trebleLevel'),
  musicReactivity: gl.getUniformLocation(prog, 'musicReactivity'),
  backLightIntensity: gl.getUniformLocation(prog, 'backLightIntensity')
};

// -------------------- Camera/orbit + state (kept exactly) --------------------
let cameraPos = [0.0, 0.0, -6.0];
let cameraRot = [0.0, 0.0];
let quality = 0.6; // unused here, but kept

let cameraOrbitEnabled = false;
let cameraOrbitRadius = 6.0;
let cameraOrbitSpeed = 0.3;
let cameraOrbitHeight = 0.0;
let manualCameraControl = true;

let timeNow = 0;
let gravityStrength = 0.6;
let gridDensityVal = 0.2;
let spacetimeWarpVal = 0.75;
let animSpeedVal = 0.25;
let backLightIntensity = 0.6;

let musicReactivity = 0.7;

// -------------------- Audio analysis --------------------
let audioContext = null, analyser = null, audioSource = null, freqData = null;
let bass=0, mid=0, treble=0;

const fileElem = document.getElementById('audioFile');
const pauseBtn = document.getElementById('pauseButton');
const audioStatus = document.getElementById('audioStatus');
let audioElement = null;
let isAudioPaused = true;

fileElem.addEventListener('change', async (e)=>{
  const file = e.target.files[0];
  if(!file) return;
  if(audioContext) {
    try { audioContext.close(); } catch {}
  }
  audioContext = new (window.AudioContext || window.webkitAudioContext)();
  analyser = audioContext.createAnalyser();
  analyser.fftSize = 512;
  freqData = new Uint8Array(analyser.frequencyBinCount);

  // create audio element (keeps original behavior)
  if(audioElement){ audioElement.pause(); audioElement.src = ''; audioElement = null; }
  audioElement = new Audio();
  audioElement.src = URL.createObjectURL(file);
  audioElement.crossOrigin = 'anonymous';
  audioElement.loop = false;

  audioSource = audioContext.createMediaElementSource(audioElement);
  audioSource.connect(analyser);
  analyser.connect(audioContext.destination);

  audioElement.addEventListener('play', ()=>{ audioStatus.textContent = 'Playing: ' + file.name; isAudioPaused=false; });
  audioElement.addEventListener('pause', ()=>{ audioStatus.textContent = 'Paused'; isAudioPaused=true; });
  audioElement.addEventListener('ended', ()=>{ audioStatus.textContent = 'Finished: ' + file.name; isAudioPaused=true; });

  await audioElement.play().catch(e=>console.warn(e));
});

// pause/play
pauseBtn.addEventListener('click', ()=>{
  if(!audioElement) return;
  if(audioElement.paused){ audioElement.play(); pauseBtn.textContent = '⏸️ PAUSE'; } 
  else { audioElement.pause(); pauseBtn.textContent = '▶️ PLAY'; }
});

// analyze audio into bass/mid/treble (same bands as original file)
function analyzeAudio(){
  if(!analyser) return;
  analyser.getByteFrequencyData(freqData);
  const len = freqData.length;
  const bassEnd = Math.floor(len * 0.1);
  const midEnd = Math.floor(len * 0.4);
  let b=0,m=0,t=0;
  for(let i=0;i<bassEnd;i++) b += freqData[i];
  for(let i=bassEnd;i<midEnd;i++) m += freqData[i];
  for(let i=midEnd;i<len;i++) t += freqData[i];
  b = (b / Math.max(1,bassEnd))/255.0;
  m = (m / Math.max(1, midEnd-bassEnd))/255.0;
  t = (t / Math.max(1,len-midEnd))/255.0;
  // keep original-style scaling
  bass = Math.pow(b, 1.0 - (0.2)); // slightly reduced sensitivity
  mid = Math.pow(m, 1.0 - (0.2));
  treble = Math.pow(t, 1.0 - (0.2));
}

// -------------------- Foreground particle sphere (2D canvas) --------------------
const ctx = canvasFG.getContext('2d');
const particleCount = 900, innerCount = 500;
const outerRadius = 200, innerRadius = 120;
const particles = [], innerParticles = [];

// fibonacci sphere distribution for uniform nodes (keeps them uniform)
function spherePoint(i, n, r){
  const phi = Math.PI * (3 - Math.sqrt(5));
  const y = 1 - (i/(n-1))*2;
  const radius = Math.sqrt(1 - y*y);
  const theta = phi * i;
  const x = Math.cos(theta) * radius;
  const z = Math.sin(theta) * radius;
  return {x:x*r, y:y*r, z:z*r, ox:x*r, oy:y*r, oz:z*r};
}
for(let i=0;i<particleCount;i++) particles.push(spherePoint(i, particleCount, outerRadius));
for(let i=0;i<innerCount;i++) innerParticles.push(spherePoint(i, innerCount, innerRadius));

function rotateY(p, angle){
  const cos = Math.cos(angle), sin = Math.sin(angle);
  return { x: p.x * cos - p.z * sin, y: p.y, z: p.x * sin + p.z * cos };
}
function project(x,y,z){
  const fov = 500;
  const scale = fov / (fov + z);
  return { x: canvasFG.width/2 + x*scale, y: canvasFG.height/2 + y*scale, s: scale };
}

// audio waveform for subtle node scaling
let timeDomain = null;
function updateWaveform(){
  if(!analyser) return;
  const tf = new Uint8Array(analyser.fftSize);
  analyser.getByteTimeDomainData(tf);
  timeDomain = tf;
}

// draw particles
let angle = 0;
function drawParticles(){
  ctx.clearRect(0,0,canvasFG.width,canvasFG.height);
  angle += 0.0035;
  // compute audioScale from waveform average (low sensitivity)
  let audioScale = 1.0;
  if(timeDomain){
    let sum=0;
    for(let i=0;i<timeDomain.length;i++) sum += Math.abs(timeDomain[i]-128);
    const avg = (sum/timeDomain.length) / 128;
    audioScale = 1 + (avg * 0.18); // lowered sensitivity
  }

  // outer
  ctx.fillStyle = 'rgba(0,200,255,0.78)';
  const nodeRadius = 2.0;
  for(const p of particles){
    const r = rotateY(p, angle);
    const px = r.x * audioScale;
    const py = r.y * audioScale;
    const pz = r.z * audioScale;
    const proj = project(px,py,pz);
    const rdraw = Math.max(0.8, nodeRadius * proj.s);
    ctx.beginPath();
    ctx.arc(proj.x, proj.y, rdraw, 0, Math.PI*2);
    ctx.fill();
  }

  // inner
  ctx.fillStyle = 'rgba(0,150,255,0.44)';
  for(const p of innerParticles){
    const r = rotateY(p, -angle*0.8);
    const px = r.x * (audioScale*0.88);
    const py = r.y * (audioScale*0.88);
    const pz = r.z * (audioScale*0.88);
    const proj = project(px,py,pz);
    const rdraw = Math.max(0.7, (nodeRadius-0.3) * proj.s);
    ctx.beginPath();
    ctx.arc(proj.x, proj.y, rdraw, 0, Math.PI*2);
    ctx.fill();
  }
}

// -------------------- Input & camera controls copied (WASD, mouse look, orbit toggle) --------------------
const keys = new Set();
window.addEventListener('keydown', e => {
  const key = e.key.toLowerCase();
  keys.add(key);
  if (key === '+' || key === '=') { quality = Math.min(1.0, quality + 0.1); resizeAll(); }
  if (key === '-') { quality = Math.max(0.1, quality - 0.1); resizeAll(); }
});
window.addEventListener('keyup', e => keys.delete(e.key.toLowerCase()));

let lastMouseMove = 0;
canvasFG.addEventListener('mousemove', e => {
  const now = performance.now();
  if (now - lastMouseMove > 16 && (e.buttons & 1) && manualCameraControl) {
    cameraRot[0] += e.movementX * 0.01;
    cameraRot[1] = Math.max(-Math.PI/2, Math.min(Math.PI/2, cameraRot[1] - e.movementY * 0.01));
    lastMouseMove = now;
  }
});

// orbit toggle: keep exactly original toggle behavior
const orbitToggle = document.createElement('button');
orbitToggle.style.display='none'; // hidden, but we keep variables consistent; original UI had one
document.body.appendChild(orbitToggle);

// -------------------- render loop: preserves camera orbit logic exactly --------------------
let lastRender = 0;
function render(now){
  updateWaveform();
  analyzeAudio();

  // Camera movement logic (copied)
  if (cameraOrbitEnabled) {
    const baseOrbitSpeed = cameraOrbitSpeed * animSpeedVal;
    const bassBoost = bass * musicReactivity * 2.0;
    const midBoost = mid * musicReactivity * 1.0;
    const trebleBoost = treble * musicReactivity * 0.5;
    const orbitAngle = timeNow * baseOrbitSpeed + bassBoost * Math.sin(timeNow * 2.0) + midBoost * Math.cos(timeNow * 1.5);
    const radiusWithBass = cameraOrbitRadius + bass * musicReactivity * 2.0;
    const heightWithMid = cameraOrbitHeight + mid * musicReactivity * 1.5;
    cameraPos[0] = Math.cos(orbitAngle) * radiusWithBass;
    cameraPos[2] = Math.sin(orbitAngle) * radiusWithBass;
    cameraPos[1] = heightWithMid + trebleBoost * Math.sin(timeNow * 4.0);
    // look at center
    const dx = 0.0 - cameraPos[0];
    const dy = 0.0 - cameraPos[1];
    const dz = 0.0 - cameraPos[2];
    const distance = Math.sqrt(dx*dx + dy*dy + dz*dz);
    cameraRot[0] = Math.atan2(dx, dz);
    cameraRot[1] = Math.asin(dy / distance);
  } else if (manualCameraControl) {
    const speed = 0.1;
    if (keys.has('w')) { cameraPos[0] += Math.sin(cameraRot[0]) * speed; cameraPos[2] += Math.cos(cameraRot[0]) * speed; }
    if (keys.has('s')) { cameraPos[0] -= Math.sin(cameraRot[0]) * speed; cameraPos[2] -= Math.cos(cameraRot[0]) * speed; }
    if (keys.has('a')) { cameraPos[0] -= Math.cos(cameraRot[0]) * speed; cameraPos[2] += Math.sin(cameraRot[0]) * speed; }
    if (keys.has('d')) { cameraPos[0] += Math.cos(cameraRot[0]) * speed; cameraPos[2] -= Math.sin(cameraRot[0]) * speed; }
    if (keys.has('q')) cameraPos[1] += speed;
    if (keys.has('e')) cameraPos[1] -= speed;
  }

  timeNow = now * 0.001;

  // WebGL draw (background grid)
  gl.useProgram(prog);
  gl.uniform2f(uniforms.resolution, canvasBG.width, canvasBG.height);
  gl.uniform3f(uniforms.cameraPos, cameraPos[0], cameraPos[1], cameraPos[2]);
  gl.uniform2f(uniforms.cameraRot, cameraRot[0], cameraRot[1]);
  gl.uniform1f(uniforms.time, timeNow);
  gl.uniform1f(uniforms.gravityStrength, gravityStrength);
  gl.uniform1f(uniforms.gridDensity, gridDensityVal);
  gl.uniform1f(uniforms.spacetimeWarp, spacetimeWarpVal + bass*0.6); // bass augments warp
  gl.uniform1f(uniforms.animSpeed, animSpeedVal);
  gl.uniform1f(uniforms.bassLevel, bass);
  gl.uniform1f(uniforms.midLevel, mid);
  gl.uniform1f(uniforms.trebleLevel, treble);
  gl.uniform1f(uniforms.musicReactivity, musicReactivity);
  gl.uniform1f(uniforms.backLightIntensity, backLightIntensity);

  gl.drawArrays(gl.TRIANGLE_STRIP, 0, 4);

  // Foreground draw
  drawParticles();

  requestAnimationFrame(render);
}
requestAnimationFrame(render);

// expose some UI toggles (orbit) similar to original
// keep exactly same variable names and behavior (you can toggle with key 'o')
window.addEventListener('keydown', (e)=>{
  if(e.key.toLowerCase() === 'o'){ cameraOrbitEnabled = !cameraOrbitEnabled; manualCameraControl = !cameraOrbitEnabled; }
  // small adjustments
  if(e.key === '[') { gridDensityVal = Math.max(0.05, gridDensityVal - 0.02); }
  if(e.key === ']') { gridDensityVal = Math.min(0.5, gridDensityVal + 0.02); }
  if(e.key === '-') { spacetimeWarpVal = Math.max(0.0, spacetimeWarpVal - 0.08); }
  if(e.key === '=') { spacetimeWarpVal = Math.min(2.0, spacetimeWarpVal + 0.08); }
});

</script>
</body>
</html>
