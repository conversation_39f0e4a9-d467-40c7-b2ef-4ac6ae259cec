<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<title>NCS Cyan <PERSON> with 140 BPM Sync</title>
<style>
html, body { margin:0; background:black; overflow:hidden; }
canvas { display:block; }
#controls { position: fixed; top: 10px; left: 10px; z-index: 10; color: white; font-family: sans-serif; }
button { background: #0ff; color: black; border: none; padding: 5px 10px; cursor: pointer; font-size: 14px; }
</style>
</head>
<body>
<div id="controls">
    <input type="file" id="audioInput" accept="audio/*">
    <button id="playPause">Play</button>
</div>
<canvas id="visualizer"></canvas>
<script>
const canvas = document.getElementById("visualizer");
const ctx = canvas.getContext("2d");
let width, height;
function resize(){ width=canvas.width=window.innerWidth; height=canvas.height=window.innerHeight; }
window.addEventListener("resize", resize);
resize();

let audioCtx, analyser, dataArray, track;
let isPlaying = false;
const playBtn = document.getElementById("playPause");

document.getElementById("audioInput").addEventListener("change", async e=>{
    const file = e.target.files[0]; if(!file) return;
    if(audioCtx) audioCtx.close();
    audioCtx = new (window.AudioContext||window.webkitAudioContext)();
    const buffer = await file.arrayBuffer();
    const audioBuffer = await audioCtx.decodeAudioData(buffer);
    track = audioCtx.createBufferSource();
    track.buffer = audioBuffer;
    analyser = audioCtx.createAnalyser();
    analyser.fftSize = 1024;
    dataArray = new Uint8Array(analyser.frequencyBinCount);
    track.connect(analyser);
    analyser.connect(audioCtx.destination);
    track.onended = () => { isPlaying = false; playBtn.textContent = "Play"; };
    track.start();
    isPlaying = true;
    playBtn.textContent = "Pause";
});

playBtn.addEventListener("click", ()=>{
    if(!audioCtx) return;
    if(isPlaying){
        audioCtx.suspend(); isPlaying=false; playBtn.textContent="Play";
    } else {
        audioCtx.resume(); isPlaying=true; playBtn.textContent="Pause";
    }
});

// --- Orb Setup ---
const nodeCount = 1400;
const nodes = [];
const goldenAngle = Math.PI*(3-Math.sqrt(5));
for(let i=0;i<nodeCount;i++){
    const y = 1-(i/(nodeCount-1))*2;
    const radius = Math.sqrt(1-y*y);
    const theta = goldenAngle*i;
    nodes.push({x:Math.cos(theta)*radius, y:y, z:Math.sin(theta)*radius, phi: Math.acos(y)});
}

let rotation=0;
let ripples=[];

// --- BPM-based timing ---
const BPM = 140;
const beatInterval = 60 / BPM;
let lastBeatTime = performance.now()/1000;

function animate(){
    requestAnimationFrame(animate);

    ctx.fillStyle="black";
    ctx.fillRect(0,0,width,height);

    let bass=0, mids=0;
    if(analyser){
        analyser.getByteFrequencyData(dataArray);
        bass = dataArray.slice(0,8).reduce((a,b)=>a+b,0)/(8*255);
        mids = dataArray.slice(8,60).reduce((a,b)=>a+b,0)/(52*255);
    }

    const now = performance.now()/1000;

    // Trigger ripple on every beat
    if(now - lastBeatTime >= beatInterval){
        lastBeatTime += beatInterval;
        ripples.push({
            time: now,
            phi: Math.random()*Math.PI,
            theta: Math.random()*2*Math.PI,
            strength: Math.max(0.3, mids*1.2)
        });
    }

    rotation += 0.002;
    const cx = width/2, cy = height/2;
    const baseRadius = Math.min(width,height)*0.25;

    // filter active ripples (3s lifetime)
    let activeRipples = ripples.filter(r=>now-r.time<3);

    for(let node of nodes){
        let displacement = 0;
        for(let r of activeRipples){
            const dx = node.phi - r.phi;
            const dy = Math.atan2(node.y, Math.sqrt(node.x*node.x+node.z*node.z)) - Math.atan2(Math.sin(r.theta), Math.cos(r.theta));
            const dist = Math.sqrt(dx*dx + dy*dy);
            // Wave frequency matches BPM (one oscillation per beat)
            const wave = Math.cos(dist*10 - ((now-r.time)/beatInterval)*2*Math.PI);
            displacement += wave * r.strength * Math.exp(-(now-r.time)*0.8);
        }

        const scale = 1 + displacement*0.12;
        let x = node.x*scale;
        let y = node.y*scale;
        let z = node.z*scale;
        const rotX = x*Math.cos(rotation)-z*Math.sin(rotation);
        const rotZ = x*Math.sin(rotation)+z*Math.cos(rotation);
        const perspective = 1/(1-rotZ*0.4);
        const x2d = rotX*baseRadius*perspective;
        const y2d = y*baseRadius*perspective;

        ctx.beginPath();
        ctx.arc(cx+x2d, cy+y2d, 1.6, 0, Math.PI*2);
        ctx.fillStyle = "hsl(180,100%,60%)";
        ctx.fill();
    }

    ripples = activeRipples;
}

animate();
</script>
</body>
</html>
