<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<title>3D Dual-Sphere Music Visualizer</title>
<style>
  html, body {
    margin: 0;
    overflow: hidden;
    background: black;
    height: 100%;
  }
  canvas { display: block; }
  #uploadBtn {
    position: absolute;
    top: 20px;
    left: 20px;
    z-index: 10;
    padding: 10px 20px;
    background: rgba(255,255,255,0.1);
    color: white;
    border: none;
    cursor: pointer;
  }
</style>
</head>
<body>
<input type="file" id="uploadBtn" accept="audio/*">
<canvas id="canvas"></canvas>

<script>
const canvas = document.getElementById('canvas');
const ctx = canvas.getContext('2d');
canvas.width = window.innerWidth;
canvas.height = window.innerHeight;

let audioCtx, analyser, dataArray;

// Spheres
const particleCount = 900;
const innerCount = 500;
const outerRadius = 200;
const innerRadius = 120;
const particles = [];
const innerParticles = [];

function randomSpherePoint(radius) {
  const u = Math.random();
  const v = Math.random();
  const theta = 2 * Math.PI * u;
  const phi = Math.acos(2 * v - 1);
  const x = radius * Math.sin(phi) * Math.cos(theta);
  const y = radius * Math.sin(phi) * Math.sin(theta);
  const z = radius * Math.cos(phi);
  return {x, y, z};
}

for (let i=0; i<particleCount; i++) particles.push(randomSpherePoint(outerRadius));
for (let i=0; i<innerCount; i++) innerParticles.push(randomSpherePoint(innerRadius));

// Perspective projection
function project(x,y,z){
  const fov = 500;
  const scale = fov / (fov + z);
  return {
    x: canvas.width/2 + x * scale,
    y: canvas.height/2 + y * scale,
    scale
  };
}

// Rotate around Y-axis
function rotateY(x,y,z,angle){
  const cos = Math.cos(angle);
  const sin = Math.sin(angle);
  return {
    x: x*cos - z*sin,
    y,
    z: x*sin + z*cos
  };
}

// Audio setup
function setupAudio(file) {
  if (audioCtx) audioCtx.close();
  audioCtx = new (window.AudioContext || window.webkitAudioContext)();
  analyser = audioCtx.createAnalyser();
  analyser.fftSize = 1024;
  const reader = new FileReader();
  reader.onload = (e) => {
    audioCtx.decodeAudioData(e.target.result, buffer => {
      const audioSource = audioCtx.createBufferSource();
      audioSource.buffer = buffer;
      audioSource.connect(analyser);
      analyser.connect(audioCtx.destination);
      audioSource.start(0);
      dataArray = new Uint8Array(analyser.frequencyBinCount);
    });
  };
  reader.readAsArrayBuffer(file);
}

// Spacetime grid
const gridSize = 40;
const gridDepth = 40;
let gridOffset = 0;

function drawGrid(){
  ctx.strokeStyle = "rgba(0,255,200,0.15)";
  ctx.lineWidth = 1;

  gridOffset += 0.5;
  if (gridOffset > gridSize) gridOffset = 0;

  for (let z=1; z<gridDepth; z++){
    const zz = z * gridSize - gridOffset;
    const p1 = project(-1000, 0, zz);
    const p2 = project(1000, 0, zz);
    ctx.beginPath();
    ctx.moveTo(p1.x, canvas.height/2 + 200* p1.scale);
    ctx.lineTo(p2.x, canvas.height/2 + 200* p2.scale);
    ctx.stroke();
  }

  for (let x=-1000; x<=1000; x+=gridSize){
    ctx.beginPath();
    const p1 = project(x,0,gridSize - gridOffset);
    const p2 = project(x,0,gridDepth*gridSize - gridOffset);
    ctx.moveTo(p1.x, canvas.height/2 + 200* p1.scale);
    ctx.lineTo(p2.x, canvas.height/2 + 200* p2.scale);
    ctx.stroke();
  }
}

let angle = 0;
function animate(){
  ctx.clearRect(0,0,canvas.width,canvas.height);
  drawGrid();

  if (analyser) analyser.getByteTimeDomainData(dataArray);
  angle += 0.002;

  function drawSphere(arr,color,intensity){
    for (let p of arr){
      const rotated = rotateY(p.x,p.y,p.z,angle);
      let scaleFactor = 1;
      if (dataArray){
        const audioVal = dataArray[Math.floor(Math.random()*dataArray.length)] / 128 - 1;
        scaleFactor = 1 + audioVal * 0.15 * intensity;
      }
      const px = rotated.x * scaleFactor;
      const py = rotated.y * scaleFactor;
      const pz = rotated.z * scaleFactor;
      const proj = project(px,py,pz);
      ctx.beginPath();
      ctx.arc(proj.x, proj.y, 2 * proj.scale, 0, Math.PI*2);
      ctx.fillStyle = color;
      ctx.fill();
    }
  }

  drawSphere(particles,"rgba(0,200,255,0.7)",1);
  drawSphere(innerParticles,"rgba(0,150,255,0.4)",0.5);

  requestAnimationFrame(animate);
}
animate();

document.getElementById('uploadBtn').addEventListener('change', (e)=>{
  const file = e.target.files[0];
  if(file) setupAudio(file);
});

window.addEventListener('resize', () => {
  canvas.width = window.innerWidth;
  canvas.height = window.innerHeight;
});
</script>
</body>
</html>
